#!/usr/bin/env python3
"""
图像匹配可视化和诊断工具
保存每对图像的详细匹配信息用于问题诊断
"""

import cv2
import numpy as np
import argparse
import json
from pathlib import Path

from src.images import Image
from src.matching.imp_matcher import IMPMatcher
from src.matching.pair_match import PairMatch
from src.config import get_config
from src.matching.multi_images_matches import natural_sort_key

def draw_keypoints(image, keypoints, title="Keypoints"):
    """绘制关键点"""
    img_with_kpts = image.copy()
    
    # 处理不同格式的keypoints
    if hasattr(keypoints[0], 'pt'):
        # OpenCV KeyPoint格式
        cv2.drawKeypoints(img_with_kpts, keypoints, img_with_kpts, 
                         color=(0, 255, 0), flags=cv2.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS)
    else:
        # numpy数组格式
        for kpt in keypoints:
            cv2.circle(img_with_kpts, (int(kpt[0]), int(kpt[1])), 3, (0, 255, 0), -1)
    
    return img_with_kpts

def draw_matches(img1, kpts1, img2, kpts2, matches, title="Matches", good_matches=None):
    """绘制匹配点"""
    # 转换keypoints格式
    if not hasattr(kpts1[0], 'pt'):
        # 从numpy数组转换为KeyPoint
        kpts1_cv = [cv2.KeyPoint(float(kpt[0]), float(kpt[1]), 1) for kpt in kpts1]
        kpts2_cv = [cv2.KeyPoint(float(kpt[0]), float(kpt[1]), 1) for kpt in kpts2]
    else:
        kpts1_cv = kpts1
        kpts2_cv = kpts2
    
    # 绘制匹配
    if good_matches is not None:
        # 分别绘制好匹配和坏匹配
        img_matches = cv2.drawMatches(
            img1, kpts1_cv, img2, kpts2_cv, matches,
            None, matchColor=(0, 255, 0), singlePointColor=(255, 0, 0),
            flags=cv2.DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS
        )
        
        # 标记坏匹配
        bad_matches = [m for i, m in enumerate(matches) if i not in good_matches]
        if bad_matches:
            img_matches = cv2.drawMatches(
                img1, kpts1_cv, img2, kpts2_cv, bad_matches,
                img_matches, matchColor=(0, 0, 255), singlePointColor=(255, 0, 0),
                flags=cv2.DrawMatchesFlags_DRAW_OVER_OUTIMG
            )
    else:
        # 绘制所有匹配
        img_matches = cv2.drawMatches(
            img1, kpts1_cv, img2, kpts2_cv, matches,
            None, matchColor=(0, 255, 0), singlePointColor=(255, 0, 0),
            flags=cv2.DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS
        )
    
    return img_matches

def warp_image(image, H, reference_shape):
    """使用单应性矩阵变形图像"""
    if H is None:
        return image
    
    # 计算变形后的图像尺寸
    h, w = reference_shape[:2]
    warped = cv2.warpPerspective(image, H, (w, h))
    return warped

def analyze_pair_match(image_a, image_b, config_mode="default", output_dir="match_analysis"):
    """分析一对图像的匹配情况"""
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True)
    
    pair_name = f"{Path(image_a.path).stem}_{Path(image_b.path).stem}"
    pair_dir = output_dir / pair_name
    pair_dir.mkdir(exist_ok=True)
    
    print(f"🔍 分析图像对: {pair_name}")
    
    # 获取配置
    config = get_config(config_mode)
    Image.sfd2_extractor = Image.sfd2_extractor.__class__(config['sfd2'])
    
    # 提取特征
    image_a.compute_features()
    image_b.compute_features()
    
    # 1. 保存关键点可视化
    print("   📍 保存关键点...")
    kpts_a = draw_keypoints(image_a.image, image_a.keypoints, f"Keypoints A ({len(image_a.keypoints)})")
    kpts_b = draw_keypoints(image_b.image, image_b.keypoints, f"Keypoints B ({len(image_b.keypoints)})")
    
    cv2.imwrite(str(pair_dir / "keypoints_a.jpg"), kpts_a)
    cv2.imwrite(str(pair_dir / "keypoints_b.jpg"), kpts_b)
    
    # 2. 进行匹配
    print("   🎯 进行特征匹配...")
    matcher = IMPMatcher(
        match_threshold=config['imp']['match_threshold'],
        max_keypoints=config['imp']['max_keypoints']
    )
    
    mkpts0, mkpts1 = matcher.match(image_a, image_b)
    
    # 转换为DMatch格式
    matches = []
    for i in range(len(mkpts0)):
        # 找到匹配点在原始keypoints中的索引
        distances_a = np.linalg.norm(image_a.keypoints - mkpts0[i], axis=1)
        distances_b = np.linalg.norm(image_b.keypoints - mkpts1[i], axis=1)
        
        idx_a = np.argmin(distances_a)
        idx_b = np.argmin(distances_b)
        
        if distances_a[idx_a] < 1e-6 and distances_b[idx_b] < 1e-6:
            match = cv2.DMatch(idx_a, idx_b, 0.0)
            matches.append(match)
    
    # 3. 保存原始匹配
    print("   💚 保存原始匹配...")
    raw_matches_img = draw_matches(
        image_a.image, image_a.keypoints, 
        image_b.image, image_b.keypoints, 
        matches, "Raw Matches"
    )
    cv2.imwrite(str(pair_dir / "raw_matches.jpg"), raw_matches_img)
    
    # 4. 计算单应性矩阵和RANSAC
    print("   🎲 计算RANSAC...")
    if len(matches) >= 4:
        # 提取匹配点
        if hasattr(image_a.keypoints[0], 'pt'):
            pts_a = np.float32([image_a.keypoints[m.queryIdx].pt for m in matches])
            pts_b = np.float32([image_b.keypoints[m.trainIdx].pt for m in matches])
        else:
            pts_a = np.float32([image_a.keypoints[m.queryIdx] for m in matches])
            pts_b = np.float32([image_b.keypoints[m.trainIdx] for m in matches])
        
        # 计算单应性矩阵
        H, status = cv2.findHomography(pts_b, pts_a, cv2.RANSAC, 5.0, maxIters=500)
        
        if H is not None:
            # 找到内点
            inliers = [i for i, s in enumerate(status) if s[0] == 1]
            outliers = [i for i, s in enumerate(status) if s[0] == 0]
            
            # 5. 保存RANSAC匹配
            print("   ✅ 保存RANSAC匹配...")
            ransac_matches_img = draw_matches(
                image_a.image, image_a.keypoints,
                image_b.image, image_b.keypoints,
                matches, "RANSAC Matches", inliers
            )
            cv2.imwrite(str(pair_dir / "ransac_matches.jpg"), ransac_matches_img)
            
            # 6. 保存变形图像
            print("   🔄 保存变形图像...")
            warped_b = warp_image(image_b.image, H, image_a.image.shape)
            cv2.imwrite(str(pair_dir / "warped_image_b.jpg"), warped_b)
            
            # 创建叠加图像
            overlay = cv2.addWeighted(image_a.image, 0.5, warped_b, 0.5, 0)
            cv2.imwrite(str(pair_dir / "overlay.jpg"), overlay)
            
            # 7. 保存统计信息
            print("   📊 保存统计信息...")
            stats = {
                "pair_name": pair_name,
                "image_a": {
                    "path": str(image_a.path),
                    "shape": image_a.image.shape,
                    "keypoints_count": len(image_a.keypoints)
                },
                "image_b": {
                    "path": str(image_b.path),
                    "shape": image_b.image.shape,
                    "keypoints_count": len(image_b.keypoints)
                },
                "matching": {
                    "raw_matches_count": len(matches),
                    "inliers_count": len(inliers),
                    "outliers_count": len(outliers),
                    "inlier_ratio": len(inliers) / len(matches) if matches else 0,
                    "homography_found": True,
                    "config_mode": config_mode
                },
                "config": config
            }
            
            with open(pair_dir / "statistics.json", 'w', encoding='utf-8') as f:
                json.dump(stats, f, indent=2, ensure_ascii=False)
            
            # 创建验证结果
            pair_match = PairMatch(image_a, image_b, matches)
            is_valid_default = pair_match.is_valid()
            is_valid_loose = pair_match.is_valid(alpha=4, beta=0.1)
            is_valid_extreme = pair_match.is_valid(alpha=2, beta=0.05)
            
            validation_result = {
                "default_validation": bool(is_valid_default),
                "loose_validation": bool(is_valid_loose),
                "extreme_validation": bool(is_valid_extreme),
                "validation_params": {
                    "default": {"alpha": 8, "beta": 0.3},
                    "loose": {"alpha": 4, "beta": 0.1},
                    "extreme": {"alpha": 2, "beta": 0.05}
                }
            }
            
            with open(pair_dir / "validation.json", 'w', encoding='utf-8') as f:
                json.dump(validation_result, f, indent=2, ensure_ascii=False)
            
            print(f"   ✅ 完成分析: {len(inliers)}/{len(matches)} 内点 ({len(inliers)/len(matches)*100:.1f}%)")
            return True
        else:
            print("   ❌ 单应性矩阵计算失败")
            return False
    else:
        print(f"   ❌ 匹配点不足: {len(matches)}")
        return False

def main():
    parser = argparse.ArgumentParser(description="图像匹配可视化和诊断")
    parser.add_argument("data_dir", type=Path, help="图像目录")
    parser.add_argument("--output", type=str, default="match_analysis", help="输出目录")
    parser.add_argument("--config", type=str, default="default", 
                       choices=["default", "fast", "loose", "ultra_loose", "high_quality"],
                       help="配置模式")
    parser.add_argument("--size", type=int, default=1000, help="图像缩放尺寸")
    parser.add_argument("--max-pairs", type=int, default=None, help="最大分析对数")
    
    args = parser.parse_args()
    
    # 获取图像列表
    valid_exts = {".jpg", ".png", ".bmp", ".jpeg"}
    image_paths = sorted([
        p for p in args.data_dir.iterdir()
        if p.suffix.lower() in valid_exts
    ], key=lambda p: natural_sort_key(str(p)))
    
    if len(image_paths) < 2:
        print("需要至少两张图像")
        return
    
    print(f"📸 找到 {len(image_paths)} 张图像")
    print(f"🔧 使用配置: {args.config}")
    print(f"📁 输出目录: {args.output}")
    
    # 加载图像
    images = [Image(str(path), args.size) for path in image_paths]
    
    # 分析相邻图像对
    output_dir = Path(args.output)
    output_dir.mkdir(exist_ok=True)
    
    pair_count = 0
    max_pairs = args.max_pairs or len(images) - 1
    
    for i in range(min(len(images) - 1, max_pairs)):
        image_a = images[i]
        image_b = images[i + 1]
        
        print(f"\n[{i+1}/{min(len(images)-1, max_pairs)}] 分析图像对:")
        print(f"  A: {Path(image_a.path).name}")
        print(f"  B: {Path(image_b.path).name}")
        
        success = analyze_pair_match(image_a, image_b, args.config, args.output)
        pair_count += 1
        
        if not success:
            print(f"  ⚠️  分析失败")
    
    print(f"\n🎉 完成！分析了 {pair_count} 对图像")
    print(f"📁 结果保存在: {output_dir.absolute()}")

if __name__ == "__main__":
    main()
