import cv2
import numpy as np

def create_geometric_mask(image_shape, margin=100):
    """
    创建几何掩码，去除边缘区域
    """
    h, w = image_shape[:2]
    mask = np.zeros((h, w), dtype=bool)
    mask[margin:h-margin, margin:w-margin] = True
    
    print(f"几何掩码信息:")
    print(f"  原始图像尺寸: {w}x{h}")
    print(f"  边缘裕度: {margin}像素")
    print(f"  有效区域尺寸: {w-2*margin}x{h-2*margin}")
    print(f"  有效像素数: {np.sum(mask)}")
    
    return mask

def apply_mask(image, mask):
    """应用掩码到图像"""
    result = image.copy()
    if len(result.shape) == 3:
        result[~mask] = [0, 0, 0]  # 无效区域设为黑色
    else:
        result[~mask] = 0
    return result

def stitch_images_with_mask(img1_path, img2_path, H, mask, output_name, pair_name):
    """
    使用几何掩码进行图像拼接
    """
    
    print(f"\n{'='*60}")
    print(f"拼接: {pair_name}")
    print(f"图像1: {img1_path}")
    print(f"图像2: {img2_path}")
    print(f"{'='*60}")
    
    # 读取图像
    img1 = cv2.imread(img1_path)
    img2 = cv2.imread(img2_path)
    
    if img1 is None or img2 is None:
        print("❌ 图像读取失败")
        return None
    
    print(f"原始图像尺寸:")
    print(f"  图像1: {img1.shape}")
    print(f"  图像2: {img2.shape}")
    
    # 应用几何掩码
    img1_masked = apply_mask(img1, mask)
    img2_masked = apply_mask(img2, mask)
    
    print(f"应用掩码后有效像素:")
    print(f"  图像1: {np.sum(img1_masked.sum(axis=2) > 0)}")
    print(f"  图像2: {np.sum(img2_masked.sum(axis=2) > 0)}")
    
    # 计算变换后的图像尺寸
    h1, w1 = img1_masked.shape[:2]
    h2, w2 = img2_masked.shape[:2]
    
    # 计算图像2的四个角点变换后的位置
    corners = np.float32([[0, 0], [w2, 0], [w2, h2], [0, h2]]).reshape(-1, 1, 2)
    transformed_corners = cv2.perspectiveTransform(corners, H)
    
    print("图像2变换后的角点:")
    for i, corner in enumerate(transformed_corners.reshape(-1, 2)):
        print(f"  角点{i+1}: ({corner[0]:.1f}, {corner[1]:.1f})")
    
    # 计算新画布的尺寸
    all_corners = np.vstack([
        [[0, 0], [w1, 0], [w1, h1], [0, h1]],  # 图像1的四个角点
        transformed_corners.reshape(-1, 2)      # 图像2变换后的四个角点
    ])
    
    x_min, y_min = np.min(all_corners, axis=0)
    x_max, y_max = np.max(all_corners, axis=0)
    
    new_width = int(x_max - x_min)
    new_height = int(y_max - y_min)
    
    print(f"新画布边界: x[{x_min:.1f}, {x_max:.1f}], y[{y_min:.1f}, {y_max:.1f}]")
    print(f"新画布尺寸: {new_width}x{new_height}")
    
    # 创建平移矩阵
    translation = np.array([
        [1, 0, -x_min],
        [0, 1, -y_min],
        [0, 0, 1]
    ])
    
    # 变换图像
    print("开始图像变换...")
    result_img1 = cv2.warpPerspective(img1_masked, translation, (new_width, new_height))
    combined_H = np.dot(translation, H)
    result_img2 = cv2.warpPerspective(img2_masked, combined_H, (new_width, new_height))
    
    print(f"变换完成:")
    print(f"  变换后图像1尺寸: {result_img1.shape}")
    print(f"  变换后图像2尺寸: {result_img2.shape}")
    
    # 计算重叠区域
    print("计算重叠区域...")
    mask1 = (result_img1.sum(axis=2) > 0)
    mask2 = (result_img2.sum(axis=2) > 0)
    overlap = mask1 & mask2
    
    print(f"区域统计:")
    print(f"  图像1有效区域: {np.sum(mask1)} 像素")
    print(f"  图像2有效区域: {np.sum(mask2)} 像素")
    print(f"  重叠区域: {np.sum(overlap)} 像素")
    
    # 灰度值加权平均融合
    print("开始融合...")
    final_result = np.zeros_like(result_img1)
    
    # 非重叠区域直接复制
    final_result[mask1 & ~overlap] = result_img1[mask1 & ~overlap]
    final_result[mask2 & ~overlap] = result_img2[mask2 & ~overlap]
    
    # 重叠区域使用灰度值加权平均
    if np.any(overlap):
        img1_overlap = result_img1[overlap].astype(np.float32)
        img2_overlap = result_img2[overlap].astype(np.float32)
        
        # 计算灰度值（BGR格式）
        gray1 = 0.114 * img1_overlap[:, 0] + 0.587 * img1_overlap[:, 1] + 0.299 * img1_overlap[:, 2]
        gray2 = 0.114 * img2_overlap[:, 0] + 0.587 * img2_overlap[:, 1] + 0.299 * img2_overlap[:, 2]
        
        # 计算权重
        epsilon = 1e-6
        weight1 = gray1 + epsilon
        weight2 = gray2 + epsilon
        
        total_weight = weight1 + weight2
        weight1_norm = weight1 / total_weight
        weight2_norm = weight2 / total_weight
        
        # 加权融合每个通道
        blended = np.zeros_like(img1_overlap)
        for c in range(3):  # BGR三个通道
            blended[:, c] = (img1_overlap[:, c] * weight1_norm + 
                            img2_overlap[:, c] * weight2_norm)
        
        final_result[overlap] = blended.astype(np.uint8)
        
        print(f"融合统计:")
        print(f"  重叠区域像素数: {np.sum(overlap)}")
        print(f"  图像1平均灰度: {np.mean(gray1):.1f}")
        print(f"  图像2平均灰度: {np.mean(gray2):.1f}")
        print(f"  图像1平均权重: {np.mean(weight1_norm):.3f}")
        print(f"  图像2平均权重: {np.mean(weight2_norm):.3f}")
    else:
        print("没有重叠区域")
    
    # 保存结果
    success = cv2.imwrite(output_name, final_result)
    if success:
        print(f"✅ 拼接成功！")
        print(f"📁 结果保存为: {output_name}")
        print(f"📊 最终尺寸: {new_width}x{new_height}")
        
        # 计算文件大小
        try:
            from pathlib import Path
            file_size = Path(output_name).stat().st_size / (1024 * 1024)  # MB
            print(f"📦 文件大小: {file_size:.1f} MB")
        except:
            pass
    else:
        print(f"❌ 保存失败")
    
    return np.sum(overlap) if np.any(overlap) else 0

def main():
    print("🚀 使用几何掩码拼接 1&3 和 2&4")
    print("="*60)
    
    # 读取样本图像获取尺寸
    sample_img = cv2.imread("D:/daima/gitup/7month/image-stitching-main/testdata3/1.bmp")
    if sample_img is None:
        print("❌ 无法读取样本图像")
        return
    
    # 创建几何掩码（边缘裕度100像素）
    mask = create_geometric_mask(sample_img.shape, margin=100)
    
    # 使用从之前测试得到的单应性矩阵
    H = np.array([
        [1.118417764482483, 0.0603628696196226, 948.2571340071909],
        [-0.0603628696196226, 1.118417764482483, -66.74286599280914],
        [0.0, 0.0, 1.0]
    ])
    
    print(f"\n使用的单应性矩阵H:")
    print(H)
    
    # 拼接 1.bmp 和 3.bmp
    overlap1 = stitch_images_with_mask(
        "D:/daima/gitup/7month/image-stitching-main/testdata3/1.bmp",
        "D:/daima/gitup/7month/image-stitching-main/testdata3/3.bmp",
        H,
        mask,
        "final_1_3_stitched.bmp",
        "1.bmp + 3.bmp"
    )
    
    # 拼接 2.bmp 和 4.bmp
    overlap2 = stitch_images_with_mask(
        "D:/daima/gitup/7month/image-stitching-main/testdata3/2.bmp",
        "D:/daima/gitup/7month/image-stitching-main/testdata3/4.bmp",
        H,
        mask,
        "final_2_4_stitched.bmp",
        "2.bmp + 4.bmp"
    )
    
    # 总结
    print(f"\n{'='*60}")
    print("🎉 拼接完成总结")
    print(f"{'='*60}")
    print(f"1.bmp + 3.bmp:")
    print(f"  重叠像素数: {overlap1}")
    print(f"  输出文件: final_1_3_stitched.bmp")
    print(f"")
    print(f"2.bmp + 4.bmp:")
    print(f"  重叠像素数: {overlap2}")
    print(f"  输出文件: final_2_4_stitched.bmp")
    print(f"")
    print(f"重叠像素数差异: {abs(overlap1 - overlap2)}")
    
    if abs(overlap1 - overlap2) == 0:
        print("✅ 完美！两对图像的重叠像素数完全相同")
    elif abs(overlap1 - overlap2) < 10:
        print("🔶 优秀！重叠像素数差异极小")
    else:
        print(f"⚠️  存在差异: {abs(overlap1 - overlap2)} 像素")
    
    print(f"\n📁 生成的文件:")
    print(f"  final_1_3_stitched.bmp - 1.bmp和3.bmp的拼接结果")
    print(f"  final_2_4_stitched.bmp - 2.bmp和4.bmp的拼接结果")
    print(f"\n🎯 使用了几何掩码（边缘裕度100像素）确保重叠区域一致性")

if __name__ == "__main__":
    main()
