import argparse  # 用于解析命令行参数
import logging   # 用于打印日志信息
import time
import torch
from pathlib import Path  # 用于处理路径对象（跨平台）

import cv2  # OpenCV库，图像处理核心工具
import numpy as np

from src.images import ImageGPU  # GPU优化的图像类
from src.matching.imp_matcher_gpu import IMPMatcherGPU  # GPU优化的特征匹配器
from src.matching import find_connected_components, build_homographies  # 图像连接图构建和单应矩阵计算
from src.rendering import set_gain_compensations, multi_band_blending, simple_blending  # 增益补偿和图像融合方法

def parse_args():
    parser = argparse.ArgumentParser(description="Panorama stitching using SFD2+IMP with GPU acceleration.")

    # 指定图像所在文件夹
    parser.add_argument("data_dir", type=Path, help="Directory containing images")

    # 图像最大边长（超过该值则缩放）
    parser.add_argument("--size", type=int, default=None, help="Resize images to max dimension")

    # 是否使用多频带融合（默认关闭）
    parser.add_argument("--multi-band", action="store_true", help="Use multi-band blending")

    # 多频带融合中的频带数量
    parser.add_argument("--num-bands", type=int, default=5, help="Number of bands for multi-band blending")

    # 多频带融合中的高斯模糊参数
    parser.add_argument("--mbb-sigma", type=float, default=1.0, help="Sigma for multi-band blending")

    # 增益补偿参数 sigma_n（用于控制噪声）
    parser.add_argument("--gain-sigma-n", type=float, default=10, help="Gain compensation sigma_n")

    # 增益补偿参数 sigma_g（用于控制梯度平滑）
    parser.add_argument("--gain-sigma-g", type=float, default=0.1, help="Gain compensation sigma_g")

    # 是否开启详细日志
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose logging")
    
    # 是否使用顺序匹配（适用于按顺序拍摄的图像）
    parser.add_argument("--sequential", action="store_true", help="Use sequential matching for images captured in order (faster)")
    
    # 顺序匹配的邻接范围
    parser.add_argument("--seq-range", type=int, default=1, help="Range for sequential matching (1=adjacent only, 2=adjacent+skip-one, etc.)")

    # GPU相关参数
    parser.add_argument("--device", type=str, default="auto", help="Device to use: 'cpu', 'cuda', 'cuda:0', or 'auto'")
    parser.add_argument("--batch-size", type=int, default=1, help="Batch size for GPU processing")
    parser.add_argument("--mixed-precision", action="store_true", help="Use mixed precision (FP16) for faster GPU processing")

    return parser.parse_args()

def setup_device(device_arg):
    """设置计算设备"""
    if device_arg == "auto":
        if torch.cuda.is_available():
            device = torch.device("cuda")
            print(f"🚀 使用GPU加速: {torch.cuda.get_device_name()}")
            print(f"   GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        else:
            device = torch.device("cpu")
            print("⚠️  CUDA不可用，使用CPU")
    else:
        device = torch.device(device_arg)
        if device.type == "cuda" and torch.cuda.is_available():
            print(f"🚀 使用指定GPU: {torch.cuda.get_device_name(device)}")
        elif device.type == "cuda":
            print("❌ 指定的CUDA设备不可用，回退到CPU")
            device = torch.device("cpu")
        else:
            print(f"💻 使用CPU")
    
    return device

def main():
    args = parse_args()  # 解析命令行参数
    if args.verbose:
        logging.basicConfig(level=logging.INFO)  # 设置日志等级为 INFO

    # 设置计算设备
    device = setup_device(args.device)
    
    # 设置混合精度
    if args.mixed_precision and device.type == "cuda":
        print("⚡ 启用混合精度加速 (FP16)")
        torch.backends.cudnn.benchmark = True
    
    # 支持的图像扩展名
    valid_exts = {".jpg", ".png", ".bmp", ".jpeg"}
    image_paths = sorted([
        str(p) for p in args.data_dir.iterdir()
        if p.suffix.lower() in valid_exts  # 过滤图像文件
    ])

    # 图像数量不足时退出
    if len(image_paths) < 2:
        logging.error("Need at least two images to stitch.")
        return
    
    print(f"📸 找到 {len(image_paths)} 张图像")
    
    # 第一步：加载图像并提取 SFD2 特征
    logging.info("Loading and extracting features...")
    start_time = time.time()
    
    images = [ImageGPU(path, args.size, device=device) for path in image_paths]  # 创建 GPU Image 对象
    
    # 批量特征提取以提高GPU利用率
    if args.batch_size > 1 and device.type == "cuda":
        print(f"🔄 批量特征提取 (batch_size={args.batch_size})")
        for i in range(0, len(images), args.batch_size):
            batch = images[i:i+args.batch_size]
            ImageGPU.batch_compute_features(batch, mixed_precision=args.mixed_precision)
    else:
        for img in images:
            img.compute_features(mixed_precision=args.mixed_precision)
    
    feature_time = time.time() - start_time
    print(f"⏱️  特征提取耗时: {feature_time:.2f}s ({feature_time/len(images):.2f}s/图像)")
    
    # 第二步：进行图像对之间的特征匹配
    logging.info("Matching features between image pairs...")
    start_time = time.time()
    
    matcher = IMPMatcherGPU(device=device, mixed_precision=args.mixed_precision)  # 实例化GPU匹配器

    # 构建所有图像的匹配图（内部会调用 matcher.match）
    from src.matching import PairMatch, MultiImageMatchesGPU
    match_graph = MultiImageMatchesGPU(images, matcher, sequential_matching=args.sequential, seq_range=args.seq_range)
    pair_matches: list[PairMatch] = match_graph.get_pair_matches()

    # 按匹配点数排序（匹配质量高的排前）
    pair_matches.sort(key=lambda pm: len(pm.matches), reverse=True)
    
    match_time = time.time() - start_time
    print(f"⏱️  特征匹配耗时: {match_time:.2f}s")
    
    # 第三步：构建连通图（找出拼接组）
    logging.info("Finding connected components...")
    connected_components = find_connected_components(pair_matches)
    logging.info(f"Found {len(connected_components)} connected components")
    
    # 第四步：为每组图像构建单应性矩阵
    logging.info("Estimating homographies...")
    build_homographies(connected_components, pair_matches)
    
    # 第五步：亮度增益补偿（避免图像亮度突变）
    logging.info("Computing gain compensation...")
    for component in connected_components:
        comp_matches = [pm for pm in pair_matches if pm.image_a in component]  # 找到当前组的匹配对
        set_gain_compensations(
            component, comp_matches,
            sigma_n=args.gain_sigma_n,
            sigma_g=args.gain_sigma_g
        )
    
    # 第六步：将增益值应用到图像上（逐像素乘 gain）
    for img in images:
        img.apply_gain()  # GPU优化的增益应用
    
    # 第七步：图像融合拼接（支持 multi-band 或 simple）
    logging.info("Stitching images...")
    start_time = time.time()
    
    if args.multi_band:
        results = [
            multi_band_blending(component, args.num_bands, args.mbb_sigma)
            for component in connected_components
        ]
    else:
        results = [
            simple_blending(component)
            for component in connected_components
        ]
    
    blend_time = time.time() - start_time
    print(f"⏱️  图像融合耗时: {blend_time:.2f}s")
    
    # 第八步：保存拼接图像结果
    output_dir = args.data_dir / "results"
    output_dir.mkdir(exist_ok=True)
    for i, result in enumerate(results):
        cv2.imwrite(str(output_dir / f"pano_{i}.jpg"), result)  # pano_0.jpg, pano_1.jpg ...

    total_time = feature_time + match_time + blend_time
    print(f"🎉 完成！总耗时: {total_time:.2f}s")
    print(f"📁 保存了 {len(results)} 个全景图到 {output_dir}")
    
    # GPU内存清理
    if device.type == "cuda":
        torch.cuda.empty_cache()
        print("🧹 GPU内存已清理")

if __name__ == "__main__":
    main()
