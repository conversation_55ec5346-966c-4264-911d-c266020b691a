import cv2
import numpy as np
import torch
from imcui.hloc.extractors.sfd2 import SFD2


class Image:
    sfd2_extractor = SFD2({'max_keypoints': 4096, 'conf_th': 0.001})

    def __init__(self, path: str, size: int = None):
        self.path = path
        self.image: np.ndarray = cv2.imread(path)
        if size is not None:
            h, w = self.image.shape[:2]
            if max(w, h) > size:
                if w > h:
                    self.image = cv2.resize(self.image, (size, int(h * size / w)))
                else:
                    self.image = cv2.resize(self.image, (int(w * size / h), size))

        self.keypoints = None
        self.features = None
        self.H: np.ndarray = np.eye(3)
        self.component_id: int = 0
        self.gain: np.ndarray = np.ones(3, dtype=np.float32)

    def compute_features(self):
        gray = cv2.cvtColor(self.image, cv2.COLOR_BGR2GRAY)
        image = np.stack([gray]*3, axis=-1)
        image_tensor = torch.from_numpy(image).float().permute(2, 0, 1).unsqueeze(0) / 255.0

        with torch.no_grad():
            pred = self.sfd2_extractor({'image': image_tensor})

        self.keypoints = pred['keypoints'][0].cpu().numpy()
        self.features = pred['descriptors'][0].cpu().numpy()
