import cv2
import numpy as np

# 读取两张图像
img1 = cv2.imread('010.jpg')  # 基准图像
img2 = cv2.imread('015.jpg')  # 要变换到img1坐标系的图像

# 使用你提供的 Homography
H = np.array([
    [1.1871787922487869, 0.027117360759767294, -377.42476589203335],
    [-0.1320779340663718, 1.0954302247326821, 785.5713923971774],
    [0.000025245110423529312, -0.00010160965006310982, 1]
])

# 获取图像大小
h1, w1 = img1.shape[:2]
h2, w2 = img2.shape[:2]

# 将图像2的四个角做透视变换
corners_img2 = np.float32([[0, 0], [0, h2], [w2, h2], [w2, 0]]).reshape(-1, 1, 2)
corners_img2_transformed = cv2.perspectiveTransform(corners_img2, H)

# 同时考虑图像1的边界
corners_img1 = np.float32([[0, 0], [0, h1], [w1, h1], [w1, 0]]).reshape(-1, 1, 2)
all_corners = np.concatenate((corners_img1, corners_img2_transformed), axis=0)

# 找到输出图像的范围
[x_min, y_min] = np.int32(all_corners.min(axis=0).ravel() - 0.5)
[x_max, y_max] = np.int32(all_corners.max(axis=0).ravel() + 0.5)

# 计算需要的平移量
translation_dist = [-x_min, -y_min]
translation_matrix = np.array([
    [1, 0, translation_dist[0]],
    [0, 1, translation_dist[1]],
    [0, 0, 1]
])

# 变换图像2
stitched_img = cv2.warpPerspective(img2, translation_matrix @ H, (x_max - x_min, y_max - y_min))

# 将图像1放置到新画布上
stitched_img[translation_dist[1]:h1+translation_dist[1], translation_dist[0]:w1+translation_dist[0]] = img1

# 显示与保存结果
cv2.imshow("Stitched Image", stitched_img)
cv2.imwrite("stitched_output.jpg", stitched_img)
cv2.waitKey(0)
cv2.destroyAllWindows()
