import torch
import numpy as np
import cv2
from imcui.hloc.matchers.imp import IMP


class IMPMatcher:
    def __init__(self, match_threshold=0.1):
        # Lower threshold to get more matches
        self.matcher = IMP({'match_threshold': match_threshold, 'features': 'sfd2'})

    def match(self, image_a, image_b):
        tensor_a = self._prepare_tensor(image_a.image)
        tensor_b = self._prepare_tensor(image_b.image)

        pred = self.matcher({
            'image0': tensor_a,
            'keypoints0': torch.from_numpy(image_a.keypoints).unsqueeze(0).float(),
            'descriptors0': torch.from_numpy(image_a.features).unsqueeze(0).float(),
            'scores0': torch.ones((1, image_a.keypoints.shape[0])),

            'image1': tensor_b,
            'keypoints1': torch.from_numpy(image_b.keypoints).unsqueeze(0).float(),
            'descriptors1': torch.from_numpy(image_b.features).unsqueeze(0).float(),
            'scores1': torch.ones((1, image_b.keypoints.shape[0])),
        })

        matches0 = pred['matches0'][0].cpu().numpy()
        valid = matches0 > -1
        mkpts0 = image_a.keypoints[valid]
        mkpts1 = image_b.keypoints[matches0[valid]]

        return mkpts0, mkpts1

    def _prepare_tensor(self, img):
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        gray3 = np.stack([gray]*3, axis=-1)
        tensor = torch.from_numpy(gray3).float().permute(2, 0, 1).unsqueeze(0) / 255.0
        return tensor
