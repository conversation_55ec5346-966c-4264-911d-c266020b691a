#!/usr/bin/env python3
"""
性能测试脚本：测量各个阶段的耗时
"""

import time
import argparse
from pathlib import Path
import logging

def time_function(func, *args, **kwargs):
    """测量函数执行时间"""
    start_time = time.time()
    result = func(*args, **kwargs)
    end_time = time.time()
    return result, end_time - start_time

def main():
    parser = argparse.ArgumentParser(description="性能测试")
    parser.add_argument("data_dir", type=Path, help="图像目录")
    parser.add_argument("--size", type=int, default=1000, help="图像尺寸")
    parser.add_argument("--count", type=int, default=6, help="测试图像数量")
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("图像拼接性能测试")
    print("=" * 60)
    
    # 测试导入时间
    print("1. 测试模块导入时间...")
    import_start = time.time()
    
    import cv2
    import numpy as np
    from src.images import Image
    from src.matching.imp_matcher import IMPMatcher
    from src.matching import PairMatch, MultiImageMatches
    
    import_time = time.time() - import_start
    print(f"   模块导入耗时: {import_time:.2f}s")
    
    # 准备测试数据
    valid_exts = {".jpg", ".png", ".bmp", ".jpeg"}
    image_paths = sorted([
        str(p) for p in args.data_dir.iterdir()
        if p.suffix.lower() in valid_exts
    ])[:args.count]
    
    if len(image_paths) < 2:
        print("需要至少两张图像进行测试")
        return
    
    print(f"2. 测试数据准备完成，使用 {len(image_paths)} 张图像")
    
    # 测试图像加载时间
    print("3. 测试图像加载时间...")
    load_start = time.time()
    images = [Image(path, args.size) for path in image_paths]
    load_time = time.time() - load_start
    print(f"   图像加载耗时: {load_time:.2f}s")
    
    # 测试特征提取时间
    print("4. 测试特征提取时间...")
    feature_start = time.time()
    for img in images:
        img.compute_features()
    feature_time = time.time() - feature_start
    print(f"   特征提取耗时: {feature_time:.2f}s")
    print(f"   平均每张图像: {feature_time/len(images):.2f}s")
    
    # 测试匹配器初始化时间
    print("5. 测试匹配器初始化时间...")
    matcher_start = time.time()
    matcher = IMPMatcher()
    matcher_init_time = time.time() - matcher_start
    print(f"   匹配器初始化耗时: {matcher_init_time:.2f}s")
    
    # 测试顺序匹配时间
    print("6. 测试顺序匹配时间...")
    match_start = time.time()
    match_graph = MultiImageMatches(images, matcher, sequential_matching=True)
    pair_matches = match_graph.get_pair_matches()
    match_time = time.time() - match_start
    print(f"   顺序匹配耗时: {match_time:.2f}s")
    print(f"   找到 {len(pair_matches)} 个有效匹配对")
    
    # 总结
    total_time = import_time + load_time + feature_time + matcher_init_time + match_time
    
    print("\n" + "=" * 60)
    print("性能总结")
    print("=" * 60)
    print(f"模块导入:     {import_time:8.2f}s ({import_time/total_time*100:5.1f}%)")
    print(f"图像加载:     {load_time:8.2f}s ({load_time/total_time*100:5.1f}%)")
    print(f"特征提取:     {feature_time:8.2f}s ({feature_time/total_time*100:5.1f}%)")
    print(f"匹配器初始化: {matcher_init_time:8.2f}s ({matcher_init_time/total_time*100:5.1f}%)")
    print(f"特征匹配:     {match_time:8.2f}s ({match_time/total_time*100:5.1f}%)")
    print("-" * 60)
    print(f"总计:         {total_time:8.2f}s (100.0%)")
    
    print("\n关键观察:")
    print(f"• Python 解释器相关开销 (导入): {import_time/total_time*100:.1f}%")
    print(f"• 深度学习计算 (特征提取+匹配): {(feature_time+match_time)/total_time*100:.1f}%")
    print(f"• 图像 I/O 操作: {load_time/total_time*100:.1f}%")
    
    if import_time/total_time < 0.05:
        print("\n结论: Python 解释器开销很小 (<5%)，打包成 EXE 不会显著提升性能")
    elif import_time/total_time < 0.10:
        print("\n结论: Python 解释器开销较小 (<10%)，打包成 EXE 提升有限")
    else:
        print("\n结论: Python 解释器开销较大，打包成 EXE 可能有一定提升")

if __name__ == "__main__":
    main()
