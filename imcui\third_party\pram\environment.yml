name: pram
channels:
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=conda_forge
  - _openmp_mutex=4.5=2_gnu
  - binutils_impl_linux-64=2.38=h2a08ee3_1
  - bzip2=1.0.8=h5eee18b_5
  - ca-certificates=2024.3.11=h06a4308_0
  - gcc=12.1.0=h9ea6d83_10
  - gcc_impl_linux-64=12.1.0=hea43390_17
  - kernel-headers_linux-64=2.6.32=he073ed8_17
  - ld_impl_linux-64=2.38=h1181459_1
  - libffi=3.4.4=h6a678d5_0
  - libgcc-devel_linux-64=12.1.0=h1ec3361_17
  - libgcc-ng=13.2.0=h807b86a_5
  - libgomp=13.2.0=h807b86a_5
  - libsanitizer=12.1.0=ha89aaad_17
  - libstdcxx-ng=13.2.0=h7e041cc_5
  - libuuid=1.41.5=h5eee18b_0
  - ncurses=6.4=h6a678d5_0
  - openssl=3.2.1=hd590300_1
  - pip=23.3.1=py310h06a4308_0
  - python=3.10.14=h955ad1f_0
  - readline=8.2=h5eee18b_0
  - setuptools=68.2.2=py310h06a4308_0
  - sqlite=3.41.2=h5eee18b_0
  - sysroot_linux-64=2.12=he073ed8_17
  - tk=8.6.12=h1ccaba5_0
  - wheel=0.41.2=py310h06a4308_0
  - xz=5.4.6=h5eee18b_0
  - zlib=1.2.13=h5eee18b_0
  - pip:
      - addict==2.4.0
      - aiofiles==23.2.1
      - aiohttp==3.9.3
      - aioopenssl==0.6.0
      - aiosasl==0.5.0
      - aiosignal==1.3.1
      - aioxmpp==0.13.3
      - asttokens==2.4.1
      - async-timeout==4.0.3
      - attrs==23.2.0
      - babel==2.14.0
      - benbotasync==3.0.2
      - blinker==1.7.0
      - certifi==2024.2.2
      - cffi==1.16.0
      - charset-normalizer==3.3.2
      - click==8.1.7
      - colorama==0.4.6
      - comm==0.2.2
      - configargparse==1.7
      - contourpy==1.2.1
      - crayons==0.4.0
      - cryptography==42.0.5
      - cycler==0.12.1
      - dash==2.16.1
      - dash-core-components==2.0.0
      - dash-html-components==2.0.0
      - dash-table==5.0.0
      - decorator==5.1.1
      - dnspython==2.6.1
      - einops==0.7.0
      - exceptiongroup==1.2.0
      - executing==2.0.1
      - fastjsonschema==2.19.1
      - filelock==3.13.3
      - flask==3.0.2
      - fonttools==4.50.0
      - fortniteapiasync==0.1.7
      - fortnitepy==3.6.9
      - frozenlist==1.4.1
      - fsspec==2024.3.1
      - h5py==3.10.0
      - html5tagger==1.3.0
      - httptools==0.6.1
      - idna==3.6
      - importlib-metadata==7.1.0
      - ipython==8.23.0
      - ipywidgets==8.1.2
      - itsdangerous==2.1.2
      - jedi==0.19.1
      - jinja2==3.1.3
      - joblib==1.3.2
      - jsonschema==4.21.1
      - jsonschema-specifications==2023.12.1
      - jupyter-core==5.7.2
      - jupyterlab-widgets==3.0.10
      - kiwisolver==1.4.5
      - lxml==4.9.4
      - markupsafe==2.1.5
      - matplotlib==3.8.4
      - matplotlib-inline==0.1.6
      - mpmath==1.3.0
      - multidict==6.0.5
      - nbformat==5.10.4
      - nest-asyncio==1.6.0
      - networkx==3.2.1
      - numpy==1.26.4
      - nvidia-cublas-cu12==********
      - nvidia-cuda-cupti-cu12==12.1.105
      - nvidia-cuda-nvrtc-cu12==12.1.105
      - nvidia-cuda-runtime-cu12==12.1.105
      - nvidia-cudnn-cu12==********
      - nvidia-cufft-cu12==*********
      - nvidia-curand-cu12==**********
      - nvidia-cusolver-cu12==**********
      - nvidia-cusparse-cu12==**********
      - nvidia-nccl-cu12==2.19.3
      - nvidia-nvjitlink-cu12==12.4.127
      - nvidia-nvtx-cu12==12.1.105
      - open3d==0.18.0
      - opencv-contrib-python==4.5.5.64
      - packaging==24.0
      - pandas==2.2.1
      - parso==0.8.3
      - pexpect==4.9.0
      - pillow==10.3.0
      - platformdirs==4.2.0
      - plotly==5.20.0
      - prompt-toolkit==3.0.43
      - ptyprocess==0.7.0
      - pure-eval==0.2.2
      - pyasn1==0.6.0
      - pyasn1-modules==0.4.0
      - pybind11==2.12.0
      - pycolmap==0.6.1
      - pycparser==2.22
      - pygments==2.17.2
      - pyopengl==3.1.7
      - pyopengl-accelerate==3.1.7
      - pyopenssl==24.1.0
      - pyparsing==3.1.2
      - pyquaternion==0.9.9
      - python-dateutil==2.9.0.post0
      - pytz==2024.1
      - pyyaml==6.0.1
      - referencing==0.34.0
      - requests==2.31.0
      - retrying==1.3.4
      - rpds-py==0.18.0
      - sanic==23.12.1
      - sanic-routing==23.12.0
      - scikit-learn==1.4.1.post1
      - scipy==1.13.0
      - six==1.16.0
      - sortedcollections==2.1.0
      - sortedcontainers==2.4.0
      - stack-data==0.6.3
      - sympy==1.12
      - tenacity==8.2.3
      - threadpoolctl==3.4.0
      - torch==2.2.2
      - torchvision==0.17.2
      - tqdm==4.66.2
      - tracerite==1.1.1
      - traitlets==5.14.2
      - triton==2.2.0
      - typing-extensions==4.10.0
      - tzdata==2024.1
      - tzlocal==5.2
      - ujson==5.9.0
      - urllib3==2.2.1
      - uvloop==0.15.2
      - wcwidth==0.2.13
      - websockets==12.0
      - werkzeug==3.0.2
      - widgetsnbextension==4.0.10
      - yaml2==0.0.1
      - yarl==1.9.4
      - zipp==3.18.1
