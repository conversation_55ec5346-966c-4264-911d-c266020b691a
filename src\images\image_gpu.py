import cv2
import numpy as np
import torch
from imcui.hloc.extractors.sfd2 import SFD2
from typing import List, Optional


class ImageGPU:
    """GPU优化的图像类，支持批量处理和混合精度"""
    
    # 共享的SFD2提取器，避免重复加载
    _sfd2_extractor = None
    _device = None
    
    @classmethod
    def _get_sfd2_extractor(cls, device):
        """获取或创建SFD2提取器"""
        if cls._sfd2_extractor is None or cls._device != device:
            cls._sfd2_extractor = SFD2({'max_keypoints': 4096, 'conf_th': 0.001})
            # 将模型移动到指定设备
            if hasattr(cls._sfd2_extractor, 'net'):
                cls._sfd2_extractor.net = cls._sfd2_extractor.net.to(device)
            cls._device = device
        return cls._sfd2_extractor

    def __init__(self, path: str, size: int = None, device: torch.device = None):
        self.path = path
        self.device = device if device is not None else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 加载图像
        self.image: np.ndarray = cv2.imread(path)
        if self.image is None:
            raise ValueError(f"无法加载图像: {path}")
            
        # 图像缩放
        if size is not None:
            h, w = self.image.shape[:2]
            if max(w, h) > size:
                if w > h:
                    self.image = cv2.resize(self.image, (size, int(h * size / w)))
                else:
                    self.image = cv2.resize(self.image, (int(w * size / h), size))

        # 初始化特征相关属性
        self.keypoints = None
        self.features = None
        self.H: np.ndarray = np.eye(3)
        self.component_id: int = 0
        self.gain: np.ndarray = np.ones(3, dtype=np.float32)
        
        # GPU相关属性
        self._image_tensor = None
        self._features_computed = False

    def _prepare_image_tensor(self, mixed_precision: bool = False):
        """准备用于GPU计算的图像张量"""
        if self._image_tensor is None:
            # 转换为灰度图并复制为3通道
            gray = cv2.cvtColor(self.image, cv2.COLOR_BGR2GRAY)
            image = np.stack([gray]*3, axis=-1)
            
            # 转换为PyTorch张量并移动到GPU
            image_tensor = torch.from_numpy(image).float().permute(2, 0, 1).unsqueeze(0) / 255.0
            self._image_tensor = image_tensor.to(self.device)
            
            # 混合精度优化
            if mixed_precision and self.device.type == 'cuda':
                self._image_tensor = self._image_tensor.half()
                
        return self._image_tensor

    def compute_features(self, mixed_precision: bool = False):
        """计算图像特征"""
        if self._features_computed:
            return
            
        # 准备输入张量
        image_tensor = self._prepare_image_tensor(mixed_precision)
        
        # 获取SFD2提取器
        extractor = self._get_sfd2_extractor(self.device)
        
        # 特征提取
        with torch.no_grad():
            if mixed_precision and self.device.type == 'cuda':
                with torch.cuda.amp.autocast():
                    pred = extractor({'image': image_tensor})
            else:
                pred = extractor({'image': image_tensor})

        # 提取结果并转换为CPU numpy数组
        self.keypoints = pred['keypoints'][0].cpu().numpy()
        self.features = pred['descriptors'][0].cpu().numpy()
        self._features_computed = True

    @classmethod
    def batch_compute_features(cls, images: List['ImageGPU'], mixed_precision: bool = False):
        """批量计算多张图像的特征，提高GPU利用率"""
        if not images:
            return
            
        device = images[0].device
        extractor = cls._get_sfd2_extractor(device)
        
        # 准备批量输入
        batch_tensors = []
        for img in images:
            if not img._features_computed:
                tensor = img._prepare_image_tensor(mixed_precision)
                batch_tensors.append(tensor)
        
        if not batch_tensors:
            return  # 所有图像都已计算过特征
            
        # 批量处理
        batch_input = torch.cat(batch_tensors, dim=0)
        
        with torch.no_grad():
            if mixed_precision and device.type == 'cuda':
                with torch.cuda.amp.autocast():
                    batch_pred = extractor({'image': batch_input})
            else:
                batch_pred = extractor({'image': batch_input})
        
        # 分配结果给各个图像
        batch_idx = 0
        for img in images:
            if not img._features_computed:
                img.keypoints = batch_pred['keypoints'][batch_idx].cpu().numpy()
                img.features = batch_pred['descriptors'][batch_idx].cpu().numpy()
                img._features_computed = True
                batch_idx += 1

    def apply_gain(self):
        """应用增益补偿，GPU优化版本"""
        if self.device.type == 'cuda':
            # 在GPU上进行增益应用
            image_tensor = torch.from_numpy(self.image).float().to(self.device)
            gain_tensor = torch.from_numpy(self.gain).to(self.device)
            
            # 应用增益
            adjusted = image_tensor * gain_tensor[None, None, :]
            adjusted = torch.clamp(adjusted, 0, 255)
            
            # 转回CPU numpy数组
            self.image = adjusted.cpu().numpy().astype(np.uint8)
        else:
            # CPU版本
            self.image = np.clip((self.image * self.gain[np.newaxis, np.newaxis, :]), 0, 255).astype(np.uint8)

    def to_device(self, device: torch.device):
        """将图像数据移动到指定设备"""
        if self.device != device:
            self.device = device
            # 清除缓存的张量，下次使用时会重新创建
            self._image_tensor = None
            # 重新计算特征（如果需要）
            if self._features_computed:
                self._features_computed = False

    def get_memory_usage(self):
        """获取GPU内存使用情况"""
        if self.device.type == 'cuda':
            return {
                'allocated': torch.cuda.memory_allocated(self.device) / 1024**2,  # MB
                'cached': torch.cuda.memory_reserved(self.device) / 1024**2,      # MB
            }
        return {'allocated': 0, 'cached': 0}

    def __del__(self):
        """析构函数，清理GPU内存"""
        if hasattr(self, '_image_tensor') and self._image_tensor is not None:
            del self._image_tensor
        if self.device.type == 'cuda':
            torch.cuda.empty_cache()
