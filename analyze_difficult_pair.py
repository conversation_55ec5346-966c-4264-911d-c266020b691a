#!/usr/bin/env python3
"""
专门分析困难图像对的脚本
"""

import cv2
import numpy as np
import json
from pathlib import Path

from src.images import Image
from src.matching.imp_matcher import IMPMatcher
from src.config import get_config

def analyze_difficult_pair():
    """分析289.bmp和290.bmp这对困难图像"""
    
    # 图像路径
    image_a_path = r"D:\daima\gitup\7month\image-stitching-main\testdata2\289.bmp"
    image_b_path = r"D:\daima\gitup\7month\image-stitching-main\testdata2\290.bmp"
    
    output_dir = Path("difficult_pair_analysis")
    output_dir.mkdir(exist_ok=True)
    
    print("🔍 分析困难图像对: 289.bmp ↔ 290.bmp")
    
    # 加载图像
    image_a = Image(image_a_path, 1000)
    image_b = Image(image_b_path, 1000)
    
    # 测试不同配置
    configs = ["default", "loose", "ultra_loose"]
    
    for config_name in configs:
        print(f"\n{'='*50}")
        print(f"测试配置: {config_name}")
        print(f"{'='*50}")
        
        # 获取配置
        config = get_config(config_name)
        print(f"SFD2参数: max_keypoints={config['sfd2']['max_keypoints']}, threshold={config['sfd2']['keypoint_threshold']}")
        print(f"IMP参数: match_threshold={config['imp']['match_threshold']}")
        
        # 应用配置
        Image.sfd2_extractor = Image.sfd2_extractor.__class__(config['sfd2'])
        
        # 提取特征
        image_a.compute_features()
        image_b.compute_features()
        
        print(f"特征点数: A={len(image_a.keypoints)}, B={len(image_b.keypoints)}")
        
        # 创建匹配器
        matcher = IMPMatcher(
            match_threshold=config['imp']['match_threshold'],
            max_keypoints=config['imp']['max_keypoints']
        )
        
        # 进行匹配
        mkpts0, mkpts1 = matcher.match(image_a, image_b)
        print(f"原始匹配点数: {len(mkpts0)}")
        
        if len(mkpts0) == 0:
            print("❌ 没有找到匹配点")
            continue
        
        # 转换为DMatch格式
        matches = []
        for i in range(len(mkpts0)):
            distances_a = np.linalg.norm(image_a.keypoints - mkpts0[i], axis=1)
            distances_b = np.linalg.norm(image_b.keypoints - mkpts1[i], axis=1)
            
            idx_a = np.argmin(distances_a)
            idx_b = np.argmin(distances_b)
            
            if distances_a[idx_a] < 1e-6 and distances_b[idx_b] < 1e-6:
                match = cv2.DMatch(idx_a, idx_b, 0.0)
                matches.append(match)
        
        print(f"有效匹配数: {len(matches)}")
        
        if len(matches) < 4:
            print("❌ 匹配点不足4个")
            continue
        
        # 计算单应性矩阵
        pts_a = np.float32([image_a.keypoints[m.queryIdx] for m in matches])
        pts_b = np.float32([image_b.keypoints[m.trainIdx] for m in matches])
        
        H, status = cv2.findHomography(pts_b, pts_a, cv2.RANSAC, 5.0, maxIters=500)
        
        if H is not None:
            inliers = np.sum(status)
            inlier_ratio = inliers / len(matches)
            print(f"内点数: {inliers}/{len(matches)} ({inlier_ratio:.1%})")
            
            # 保存可视化结果
            config_dir = output_dir / config_name
            config_dir.mkdir(exist_ok=True)
            
            # 1. 保存关键点
            kpts_a = image_a.image.copy()
            kpts_b = image_b.image.copy()
            
            for kpt in image_a.keypoints:
                cv2.circle(kpts_a, (int(kpt[0]), int(kpt[1])), 3, (0, 255, 0), -1)
            for kpt in image_b.keypoints:
                cv2.circle(kpts_b, (int(kpt[0]), int(kpt[1])), 3, (0, 255, 0), -1)
            
            cv2.imwrite(str(config_dir / "keypoints_289.jpg"), kpts_a)
            cv2.imwrite(str(config_dir / "keypoints_290.jpg"), kpts_b)
            
            # 2. 保存原始匹配
            kpts_a_cv = [cv2.KeyPoint(float(kpt[0]), float(kpt[1]), 1) for kpt in image_a.keypoints]
            kpts_b_cv = [cv2.KeyPoint(float(kpt[0]), float(kpt[1]), 1) for kpt in image_b.keypoints]
            
            raw_matches_img = cv2.drawMatches(
                image_a.image, kpts_a_cv, image_b.image, kpts_b_cv, matches,
                None, matchColor=(0, 255, 0), singlePointColor=(255, 0, 0),
                flags=cv2.DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS
            )
            cv2.imwrite(str(config_dir / "raw_matches.jpg"), raw_matches_img)
            
            # 3. 保存RANSAC匹配
            inlier_indices = [i for i, s in enumerate(status) if s[0] == 1]
            outlier_indices = [i for i, s in enumerate(status) if s[0] == 0]
            
            # 先绘制内点（绿色）
            ransac_img = cv2.drawMatches(
                image_a.image, kpts_a_cv, image_b.image, kpts_b_cv, 
                [matches[i] for i in inlier_indices],
                None, matchColor=(0, 255, 0), singlePointColor=(255, 0, 0),
                flags=cv2.DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS
            )
            
            # 再绘制外点（红色）
            if outlier_indices:
                ransac_img = cv2.drawMatches(
                    image_a.image, kpts_a_cv, image_b.image, kpts_b_cv,
                    [matches[i] for i in outlier_indices],
                    ransac_img, matchColor=(0, 0, 255), singlePointColor=(255, 0, 0),
                    flags=cv2.DrawMatchesFlags_DRAW_OVER_OUTIMG
                )
            
            cv2.imwrite(str(config_dir / "ransac_matches.jpg"), ransac_img)
            
            # 4. 保存变形图像
            warped_b = cv2.warpPerspective(image_b.image, H, (image_a.image.shape[1], image_a.image.shape[0]))
            cv2.imwrite(str(config_dir / "warped_290.jpg"), warped_b)
            
            # 5. 保存叠加图像
            overlay = cv2.addWeighted(image_a.image, 0.5, warped_b, 0.5, 0)
            cv2.imwrite(str(config_dir / "overlay.jpg"), overlay)
            
            # 6. 保存统计信息
            stats = {
                "config": config_name,
                "sfd2_params": config['sfd2'],
                "imp_params": config['imp'],
                "keypoints_289": len(image_a.keypoints),
                "keypoints_290": len(image_b.keypoints),
                "raw_matches": len(mkpts0),
                "valid_matches": len(matches),
                "inliers": int(inliers),
                "outliers": len(matches) - int(inliers),
                "inlier_ratio": float(inlier_ratio),
                "homography_success": True
            }
            
            with open(config_dir / "statistics.json", 'w', encoding='utf-8') as f:
                json.dump(stats, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 保存到: {config_dir}")
        else:
            print("❌ 单应性矩阵计算失败")
    
    print(f"\n🎉 分析完成！结果保存在: {output_dir.absolute()}")
    print("\n📋 文件说明:")
    print("  keypoints_289.jpg / keypoints_290.jpg - 关键点可视化")
    print("  raw_matches.jpg - 原始匹配（绿色线条）")
    print("  ransac_matches.jpg - RANSAC匹配（绿色=内点，红色=外点）")
    print("  warped_290.jpg - 变形后的290图像")
    print("  overlay.jpg - 289和变形后290的叠加")
    print("  statistics.json - 详细统计信息")

if __name__ == "__main__":
    analyze_difficult_pair()
