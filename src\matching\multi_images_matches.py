import cv2
import numpy as np
import re
from pathlib import Path

from src.images import Image
from src.matching.pair_match import PairMatch


def natural_sort_key(path_str: str):
    """
    生成用于自然排序的键，使得数字按数值大小排序而不是字符串排序
    例如: 1.bmp, 2.bmp, 10.bmp 而不是 1.bmp, 10.bmp, 2.bmp
    """
    filename = Path(path_str).stem  # 获取不带扩展名的文件名
    # 将文件名中的数字部分转换为整数进行排序
    parts = re.split(r'(\d+)', filename)
    result = []
    for part in parts:
        if part.isdigit():
            result.append(int(part))
        else:
            result.append(part)
    return result


class MultiImageMatches:
    def __init__(self, images: list[Image], matcher, ratio: float = 0.75, sequential_matching: bool = False, seq_range: int = 1) -> None:
        """
        Create a new MultiImageMatches object.

        Args:
            images: images to compare
            matcher: feature matcher to use (e.g., IMPMatcher)
            ratio: ratio used for the Lowe's ratio test
            sequential_matching: if True, only match adjacent images based on filename order
            seq_range: range for sequential matching (1=adjacent only, 2=adjacent+skip-one, etc.)
        """
        self.images = images
        self.matches = {image.path: {} for image in images}
        self.matcher = matcher
        self.ratio = ratio
        self.sequential_matching = sequential_matching
        self.seq_range = seq_range

    def get_matches(self, image_a: Image, image_b: Image) -> list:
        """
        Get matches for the given images.

        Args:
            image_a: First image
            image_b: Second image

        Returns:
            matches: List of matches between the two images
        """
        if image_b.path not in self.matches[image_a.path]:
            matches = self.compute_matches(image_a, image_b)
            self.matches[image_a.path][image_b.path] = matches

        return self.matches[image_a.path][image_b.path]

    def get_pair_matches(self, max_images: int = 6) -> list[PairMatch]:
        """
        Get the pair matches for the given images.

        Args:
            max_images: Number of matches maximum for each image

        Returns:
            pair_matches: List of pair matches
        """
        if self.sequential_matching:
            return self._get_sequential_pair_matches()
        else:
            return self._get_all_pair_matches(max_images)

    def _get_sequential_pair_matches(self) -> list[PairMatch]:
        """
        Get pair matches for sequential images based on filename order.
        This is much faster when images are captured in sequence.

        Returns:
            pair_matches: List of pair matches for adjacent images
        """
        pair_matches = []
        print(f"Using sequential matching for {len(self.images)} images with range {self.seq_range}...")

        # Sort images by filename using natural sorting (numeric order)
        sorted_images = sorted(self.images, key=lambda img: natural_sort_key(img.path))

        print("图像排序结果:")
        for i, img in enumerate(sorted_images):
            print(f"  {i}: {Path(img.path).name}")
        print()

        # Match each image with the next seq_range images in sequence
        for i in range(len(sorted_images)):
            for j in range(1, min(self.seq_range + 1, len(sorted_images) - i)):
                image_a = sorted_images[i]
                image_b = sorted_images[i + j]

                print(f"Matching {image_a.path} with {image_b.path} (gap: {j})")
                matches = self.get_matches(image_a, image_b)

                if len(matches) > 0:
                    pair_match = PairMatch(image_a, image_b, matches)
                    if pair_match.is_valid():
                        pair_matches.append(pair_match)
                        print(f"  -> Valid match found with {len(matches)} feature matches")
                    else:
                        print(f"  -> Invalid match (insufficient inliers)")
                else:
                    print(f"  -> No feature matches found")

        return pair_matches

    def _get_all_pair_matches(self, max_images: int = 6) -> list[PairMatch]:
        """
        Get pair matches using the original all-to-all matching strategy.

        Args:
            max_images: Number of matches maximum for each image

        Returns:
            pair_matches: List of pair matches
        """
        pair_matches = []
        for i, image_a in enumerate(self.images):
            possible_matches = sorted(
                self.images[:i] + self.images[i + 1 :],
                key=lambda image, ref=image_a: len(self.get_matches(ref, image)),
                reverse=True,
            )[:max_images]
            for image_b in possible_matches:
                if self.images.index(image_b) > i:
                    pair_match = PairMatch(image_a, image_b, self.get_matches(image_a, image_b))
                    if pair_match.is_valid():
                        pair_matches.append(pair_match)
        return pair_matches

    def compute_matches(self, image_a: Image, image_b: Image) -> list:
        """
        Compute matches between image_a and image_b.

        Args:
            image_a: First image
            image_b: Second image

        Returns:
            matches: List of matches between image_a and image_b
        """
        # Use the provided matcher (e.g., IMPMatcher) instead of OpenCV BruteForce
        mkpts0, mkpts1 = self.matcher.match(image_a, image_b)

        # Convert matched keypoints to cv2.DMatch format for compatibility
        matches = []
        for i in range(len(mkpts0)):
            # Find indices of matched keypoints in original keypoint arrays
            # For SFD2 features, keypoints are stored as numpy arrays
            idx0 = self._find_keypoint_index(image_a.keypoints, mkpts0[i])
            idx1 = self._find_keypoint_index(image_b.keypoints, mkpts1[i])

            if idx0 is not None and idx1 is not None:
                # Create DMatch object for compatibility with existing code
                match = cv2.DMatch(idx0, idx1, 0.0)  # distance set to 0 since IMP already filtered
                matches.append(match)

        return matches

    def _find_keypoint_index(self, keypoints: np.ndarray, target_point: np.ndarray) -> int:
        """
        Find the index of a keypoint in the keypoints array.

        Args:
            keypoints: Array of keypoints
            target_point: Target keypoint to find

        Returns:
            Index of the keypoint, or None if not found
        """
        # For SFD2, keypoints are stored as [N, 2] numpy array
        distances = np.linalg.norm(keypoints - target_point, axis=1)
        min_idx = np.argmin(distances)

        # Check if the closest point is actually the same (within small tolerance)
        if distances[min_idx] < 1e-6:
            return min_idx
        return None
