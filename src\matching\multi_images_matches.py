import cv2
import numpy as np

from src.images import Image
from src.matching.pair_match import PairMatch


class MultiImageMatches:
    def __init__(self, images: list[Image], matcher, ratio: float = 0.75) -> None:
        """
        Create a new MultiImageMatches object.

        Args:
            images: images to compare
            matcher: feature matcher to use (e.g., IMPMatcher)
            ratio: ratio used for the Lowe's ratio test
        """
        self.images = images
        self.matches = {image.path: {} for image in images}
        self.matcher = matcher
        self.ratio = ratio

    def get_matches(self, image_a: Image, image_b: Image) -> list:
        """
        Get matches for the given images.

        Args:
            image_a: First image
            image_b: Second image

        Returns:
            matches: List of matches between the two images
        """
        if image_b.path not in self.matches[image_a.path]:
            matches = self.compute_matches(image_a, image_b)
            self.matches[image_a.path][image_b.path] = matches

        return self.matches[image_a.path][image_b.path]

    def get_pair_matches(self, max_images: int = 6) -> list[PairMatch]:
        """
        Get the pair matches for the given images.

        Args:
            max_images: Number of matches maximum for each image

        Returns:
            pair_matches: List of pair matches
        """
        pair_matches = []
        for i, image_a in enumerate(self.images):
            possible_matches = sorted(
                self.images[:i] + self.images[i + 1 :],
                key=lambda image, ref=image_a: len(self.get_matches(ref, image)),
                reverse=True,
            )[:max_images]
            for image_b in possible_matches:
                if self.images.index(image_b) > i:
                    pair_match = PairMatch(image_a, image_b, self.get_matches(image_a, image_b))
                    if pair_match.is_valid():
                        pair_matches.append(pair_match)
        return pair_matches

    def compute_matches(self, image_a: Image, image_b: Image) -> list:
        """
        Compute matches between image_a and image_b.

        Args:
            image_a: First image
            image_b: Second image

        Returns:
            matches: List of matches between image_a and image_b
        """
        # Use the provided matcher (e.g., IMPMatcher) instead of OpenCV BruteForce
        mkpts0, mkpts1 = self.matcher.match(image_a, image_b)

        # Convert matched keypoints to cv2.DMatch format for compatibility
        matches = []
        for i in range(len(mkpts0)):
            # Find indices of matched keypoints in original keypoint arrays
            # For SFD2 features, keypoints are stored as numpy arrays
            idx0 = self._find_keypoint_index(image_a.keypoints, mkpts0[i])
            idx1 = self._find_keypoint_index(image_b.keypoints, mkpts1[i])

            if idx0 is not None and idx1 is not None:
                # Create DMatch object for compatibility with existing code
                match = cv2.DMatch(idx0, idx1, 0.0)  # distance set to 0 since IMP already filtered
                matches.append(match)

        return matches

    def _find_keypoint_index(self, keypoints: np.ndarray, target_point: np.ndarray) -> int:
        """
        Find the index of a keypoint in the keypoints array.

        Args:
            keypoints: Array of keypoints
            target_point: Target keypoint to find

        Returns:
            Index of the keypoint, or None if not found
        """
        # For SFD2, keypoints are stored as [N, 2] numpy array
        distances = np.linalg.norm(keypoints - target_point, axis=1)
        min_idx = np.argmin(distances)

        # Check if the closest point is actually the same (within small tolerance)
        if distances[min_idx] < 1e-6:
            return min_idx
        return None
