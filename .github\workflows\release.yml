name: PyPI Release
on:
  release:
    types: [published]

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        platform: [ubuntu-latest]
        python-version: ["3.9", "3.10", "3.11"]

    runs-on: ${{ matrix.platform }}
    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive

    - uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Upgrade setuptools and wheel
      run: |
        pip install --upgrade setuptools wheel

    - name: Install dependencies on Ubuntu
      if: runner.os == 'Linux'
      run: |
        sudo apt-get update
        sudo apt-get install libopencv-dev -y

    - name: Install dependencies on macOS
      if: runner.os == 'macOS'
      run: |
        brew update
        brew install opencv

    - name: Install dependencies on Windows
      if: runner.os == 'Windows'
      run: |
        choco install opencv -y

    - name: Add requirements
      run: python -m pip install --upgrade setuptools wheel build

    - name: Install Python dependencies
      run: |
        pip install pytest
        pip install -r requirements.txt
        sudo apt-get update && sudo apt-get install ffmpeg libsm6 libxext6 -y

    - name: Build source distribution
      run: |
        python -m build --outdir dist/
        ls -lh dist/

    - name: Upload to GitHub Release
      if: matrix.python-version == '3.10' && github.event_name == 'release'
      uses: softprops/action-gh-release@v2
      with:
        files: dist/*.whl
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Archive wheels
      if: matrix.python-version == '3.10' && github.event_name == 'release'
      uses: actions/upload-artifact@v4
      with:
        name: dist
        path: dist/*.whl


  pypi-publish:
    name: upload release to PyPI
    needs: build
    runs-on: ubuntu-latest
    environment: pypi
    permissions:
      # IMPORTANT: this permission is mandatory for Trusted Publishing
      id-token: write
    steps:
      # retrieve your distributions here
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          name: dist
          path: dist

      - name: List dist directory
        run: ls -lh dist/

      - name: Publish package distributions to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
