"""
配置管理模块
支持不同的预设配置和自定义参数
"""

# 默认配置（基于您提供的配置文件）
DEFAULT_CONFIG = {
    "sfd2": {
        "max_keypoints": 1000,
        "keypoint_threshold": 0.015,
        "conf_th": 0.001
    },
    "imp": {
        "match_threshold": 0.1,
        "max_keypoints": 1000
    }
}

# 快速模式配置
FAST_CONFIG = {
    "sfd2": {
        "max_keypoints": 500,
        "keypoint_threshold": 0.02,
        "conf_th": 0.002
    },
    "imp": {
        "match_threshold": 0.2,
        "max_keypoints": 500
    }
}

# 宽松模式配置
LOOSE_CONFIG = {
    "sfd2": {
        "max_keypoints": 2000,
        "keypoint_threshold": 0.01,
        "conf_th": 0.0005
    },
    "imp": {
        "match_threshold": 0.05,
        "max_keypoints": 2000
    }
}

# 超宽松模式配置
ULTRA_LOOSE_CONFIG = {
    "sfd2": {
        "max_keypoints": 4000,
        "keypoint_threshold": 0.005,
        "conf_th": 0.0001
    },
    "imp": {
        "match_threshold": 0.01,
        "max_keypoints": 4000
    }
}

# 高质量模式配置
HIGH_QUALITY_CONFIG = {
    "sfd2": {
        "max_keypoints": 4096,
        "keypoint_threshold": 0.01,
        "conf_th": 0.0005
    },
    "imp": {
        "match_threshold": 0.08,
        "max_keypoints": 4096
    }
}

# 超高质量模式配置（针对困难序列）
SUPER_HIGH_QUALITY_CONFIG = {
    "sfd2": {
        "max_keypoints": 8192,
        "keypoint_threshold": 0.005,
        "conf_th": 0.0001
    },
    "imp": {
        "match_threshold": 0.05,
        "max_keypoints": 8192
    }
}

def get_config(mode="default"):
    """
    获取指定模式的配置

    Args:
        mode: 配置模式 ("default", "fast", "loose", "ultra_loose", "high_quality", "super_high_quality")

    Returns:
        配置字典
    """
    configs = {
        "default": DEFAULT_CONFIG,
        "fast": FAST_CONFIG,
        "loose": LOOSE_CONFIG,
        "ultra_loose": ULTRA_LOOSE_CONFIG,
        "high_quality": HIGH_QUALITY_CONFIG,
        "super_high_quality": SUPER_HIGH_QUALITY_CONFIG
    }

    return configs.get(mode, DEFAULT_CONFIG)

def print_config(config):
    """打印配置信息"""
    print("Configuration:")
    print(f"  SFD2:")
    print(f"    max_keypoints: {config['sfd2']['max_keypoints']}")
    print(f"    keypoint_threshold: {config['sfd2']['keypoint_threshold']}")
    print(f"    conf_th: {config['sfd2']['conf_th']}")
    print(f"  IMP:")
    print(f"    match_threshold: {config['imp']['match_threshold']}")
    print(f"    max_keypoints: {config['imp']['max_keypoints']}")
