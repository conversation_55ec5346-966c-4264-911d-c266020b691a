# import argparse
# import logging
# import time
# from pathlib import Path
# import cv2
# import numpy as np

# from src.images import Image
# from src.matching.imp_matcher import IMPMatcher


# def stitch_two_images(img1, img2, H):
#     h1, w1 = img1.shape[:2]
#     h2, w2 = img2.shape[:2]

#     corners1 = np.float32([[0, 0], [0, h1], [w1, h1], [w1, 0]]).reshape(-1, 1, 2)
#     corners1_trans = cv2.perspectiveTransform(corners1, H)
#     corners2 = np.float32([[0, 0], [0, h2], [w2, h2], [w2, 0]]).reshape(-1, 1, 2)
#     all_corners = np.concatenate((corners1_trans, corners2), axis=0)

#     [xmin, ymin] = np.floor(np.min(all_corners, axis=0).ravel()).astype(int)
#     [xmax, ymax] = np.ceil(np.max(all_corners, axis=0).ravel()).astype(int)
#     translation = [-xmin, -ymin]
#     output_size = (xmax - xmin, ymax - ymin)

#     H_translation = np.array([[1, 0, translation[0]],
#                               [0, 1, translation[1]],
#                               [0, 0, 1]])

#     warped_img1 = cv2.warpPerspective(img1, H_translation @ H, output_size)
#     mask1 = (warped_img1 > 0).astype(np.float32)

#     canvas_img2 = np.zeros_like(warped_img1, dtype=np.uint8)
#     canvas_img2[translation[1]:translation[1]+h2, translation[0]:translation[0]+w2] = img2
#     mask2 = (canvas_img2 > 0).astype(np.float32)

#     weight1 = cv2.GaussianBlur(mask1, (51, 51), 0)
#     weight2 = cv2.GaussianBlur(mask2, (51, 51), 0)
#     total_weight = weight1 + weight2
#     total_weight[total_weight == 0] = 1

#     blended = (warped_img1.astype(np.float32) * weight1 + canvas_img2.astype(np.float32) * weight2) / total_weight
#     return np.clip(blended, 0, 255).astype(np.uint8)


# if __name__ == "__main__":
#     parser = argparse.ArgumentParser()
#     parser.add_argument("data_dir", type=Path, help="directory containing the images")
#     parser.add_argument("--size", type=int, help="resize images to max dimension")
#     args = parser.parse_args()

#     logging.basicConfig(level=logging.INFO)
#     image_paths = sorted([
#         str(p) for p in args.data_dir.iterdir()
#         if p.suffix.lower() in [".jpg", ".png", ".bmp", ".jpeg"]
#     ])

#     images = [Image(path, args.size) for path in image_paths]
#     for img in images:
#         img.compute_features()

#     matcher = IMPMatcher()

#     output_dir = args.data_dir / "results"
#     output_dir.mkdir(exist_ok=True)

#     for i in range(len(images) - 1):
#         imgA, imgB = images[i], images[i + 1]
#         mkptsA, mkptsB = matcher.match(imgA, imgB)

#         if len(mkptsA) >= 4:
#             H, mask = cv2.findHomography(mkptsA, mkptsB, cv2.RANSAC, 3.0)
#             inliers = mask.ravel().astype(bool)
#             logging.info(f"Pair {i}-{i+1}: Inliers: {np.sum(inliers)} / {len(mkptsA)}")

#             result = stitch_two_images(imgA.image, imgB.image, H)
#             cv2.imwrite(str(output_dir / f"pano_{i}_{i+1}.jpg"), result)
#         else:
#             logging.warning(f"Pair {i}-{i+1}: Not enough matches.")


import argparse
import logging
import time
from pathlib import Path
import cv2
import numpy as np

from src.images import Image
from src.matching.imp_matcher import IMPMatcher


def stitch_images(images, matcher):
    canvas = images[0].image
    H_accum = np.eye(3)  # 累积单应矩阵，初始为单位矩阵

    for i in range(len(images) - 1):
        imgA, imgB = images[i], images[i + 1]
        mkptsA, mkptsB = matcher.match(imgA, imgB)

        if len(mkptsA) < 4:
            logging.warning(f"Pair {i}-{i+1}: Not enough matches, skipping.")
            continue

        # 计算从 imgB 到 imgA 的单应矩阵
        H, mask = cv2.findHomography(mkptsB, mkptsA, cv2.RANSAC, 3.0)
        H_accum = H_accum @ H  # 累积变换

        # 计算当前画布和新图像大小
        h_canvas, w_canvas = canvas.shape[:2]
        h_next, w_next = imgB.image.shape[:2]

        # 变换新图四个角点到画布坐标系
        corners = np.array([[0, 0], [0, h_next], [w_next, h_next], [w_next, 0]], dtype=np.float32)
        corners = cv2.perspectiveTransform(corners.reshape(-1, 1, 2), H_accum).reshape(-1, 2)

        # 合并画布与新图角点，计算新的画布范围
        all_corners = np.vstack((np.array([[0, 0], [0, h_canvas], [w_canvas, h_canvas], [w_canvas, 0]]), corners))
        xmin, ymin = np.floor(all_corners.min(axis=0)).astype(int)
        xmax, ymax = np.ceil(all_corners.max(axis=0)).astype(int)

        translation = np.array([[1, 0, -xmin],
                        [0, 1, -ymin],
                        [0, 0, 1]], dtype=np.float64)


        new_w, new_h = xmax - xmin, ymax - ymin

        # 对已有画布进行仿射变换以适应新画布大小
        canvas_warped = cv2.warpPerspective(canvas, translation, (new_w, new_h))
        # 对新图进行仿射变换并叠加到画布
        imgB_warped = cv2.warpPerspective(imgB.image, translation @ H_accum, (new_w, new_h))

        # 融合权重
        mask_canvas = (canvas_warped > 0).astype(np.float32)
        mask_imgB = (imgB_warped > 0).astype(np.float32)
        weight_canvas = cv2.GaussianBlur(mask_canvas, (51, 51), 0)
        weight_imgB = cv2.GaussianBlur(mask_imgB, (51, 51), 0)
        total_weight = weight_canvas + weight_imgB
        total_weight[total_weight == 0] = 1

        # 融合图像
        canvas = (canvas_warped * weight_canvas + imgB_warped * weight_imgB) / total_weight
        canvas = canvas.astype(np.uint8)

    return canvas


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Stitch multiple images into a panorama.")
    parser.add_argument("data_dir", type=Path, help="Directory containing images")
    parser.add_argument("--size", type=int, default=None, help="Resize images max dimension")
    args = parser.parse_args()

    logging.basicConfig(level=logging.INFO)

    # 读取图像路径，支持jpg/png/bmp/jpeg格式
    image_paths = sorted([
        str(p) for p in args.data_dir.iterdir()
        if p.suffix.lower() in [".jpg", ".png", ".bmp", ".jpeg"]
    ])

    if len(image_paths) < 2:
        logging.error("Need at least two images to stitch.")
        exit(1)

    # 读取图片对象并计算特征
    images = [Image(path, args.size) for path in image_paths]
    for img in images:
        img.compute_features()

    matcher = IMPMatcher()

    # 拼接所有图片
    panorama = stitch_images(images, matcher)

    output_dir = args.data_dir / "results"
    output_dir.mkdir(exist_ok=True)
    output_path = output_dir / "full_panorama.jpg"
    cv2.imwrite(str(output_path), panorama)

    logging.info(f"Panorama saved to {output_path}")
