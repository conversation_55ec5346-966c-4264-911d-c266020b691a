#!/usr/bin/env python3
"""
改进的图像拼接算法
使用全局优化解决累积误差问题
"""

import cv2
import numpy as np
import argparse
from pathlib import Path

from src.images import Image
from src.matching.imp_matcher import IMPMatcher
from src.config import get_config
from src.matching.multi_images_matches import natural_sort_key

def estimate_camera_parameters(images, matches_data):
    """估计相机参数（简化版Bundle Adjustment）"""
    
    print("🔧 估计全局相机参数...")
    
    n_images = len(images)
    
    # 初始化相机参数（旋转角度）
    # 假设相机只有水平旋转，简化问题
    camera_angles = np.zeros(n_images)
    
    # 从匹配数据估计相对旋转
    for match_data in matches_data:
        i, j = match_data['image_indices']
        H = match_data['homography']
        
        if H is not None:
            # 从单应性矩阵估计旋转角度
            # 简化假设：只有旋转和平移，没有透视变形
            angle = np.arctan2(H[0, 1], H[0, 0])
            
            # 累积角度（简单方法）
            if j == i + 1:  # 相邻图像
                camera_angles[j] = camera_angles[i] + angle
    
    # 平滑角度（减少累积误差）
    # 使用简单的移动平均
    smoothed_angles = camera_angles.copy()
    for i in range(1, n_images - 1):
        smoothed_angles[i] = (camera_angles[i-1] + camera_angles[i] + camera_angles[i+1]) / 3
    
    return smoothed_angles

def create_cylindrical_projection(images, camera_angles, focal_length=1000):
    """创建柱面投影"""
    
    print("🌀 创建柱面投影...")
    
    projected_images = []
    
    for i, (image, angle) in enumerate(zip(images, camera_angles)):
        img = image.image
        h, w = img.shape[:2]
        
        # 创建柱面投影的映射
        map_x = np.zeros((h, w), dtype=np.float32)
        map_y = np.zeros((h, w), dtype=np.float32)
        
        cx, cy = w // 2, h // 2
        
        for y in range(h):
            for x in range(w):
                # 转换到柱面坐标
                theta = (x - cx) / focal_length
                phi = (y - cy) / focal_length
                
                # 应用相机旋转
                theta_rot = theta + angle
                
                # 投影回图像坐标
                x_proj = focal_length * theta_rot + cx
                y_proj = focal_length * phi + cy
                
                map_x[y, x] = x_proj
                map_y[y, x] = y_proj
        
        # 应用映射
        projected = cv2.remap(img, map_x, map_y, cv2.INTER_LINEAR)
        projected_images.append(projected)
        
        print(f"  投影图像 {i+1}/{len(images)}: {Path(image.path).name}")
    
    return projected_images

def simple_blending_improved(projected_images, camera_angles, focal_length=1000):
    """改进的简单融合"""
    
    print("🎨 进行图像融合...")
    
    # 计算全景图尺寸
    total_angle = max(camera_angles) - min(camera_angles)
    pano_width = int(focal_length * total_angle) + projected_images[0].shape[1]
    pano_height = max(img.shape[0] for img in projected_images)
    
    print(f"  全景图尺寸: {pano_width} x {pano_height}")
    
    # 创建全景图
    panorama = np.zeros((pano_height, pano_width, 3), dtype=np.uint8)
    weight_map = np.zeros((pano_height, pano_width), dtype=np.float32)
    
    min_angle = min(camera_angles)
    
    for i, (img, angle) in enumerate(zip(projected_images, camera_angles)):
        h, w = img.shape[:2]
        
        # 计算在全景图中的位置
        x_offset = int(focal_length * (angle - min_angle))
        y_offset = (pano_height - h) // 2
        
        # 确保不越界
        x_end = min(x_offset + w, pano_width)
        y_end = min(y_offset + h, pano_height)
        
        if x_offset < pano_width and y_offset < pano_height:
            # 计算实际复制的区域
            img_w = x_end - x_offset
            img_h = y_end - y_offset
            
            if img_w > 0 and img_h > 0:
                # 创建权重（中心权重高，边缘权重低）
                weight = np.ones((img_h, img_w), dtype=np.float32)
                
                # 边缘羽化
                fade_width = min(50, img_w // 4)
                for j in range(fade_width):
                    weight[:, j] *= j / fade_width
                    weight[:, -(j+1)] *= j / fade_width
                
                # 融合图像
                roi_pano = panorama[y_offset:y_end, x_offset:x_end]
                roi_weight = weight_map[y_offset:y_end, x_offset:x_end]
                roi_img = img[:img_h, :img_w]
                
                # 加权平均
                total_weight = roi_weight + weight
                mask = total_weight > 0
                
                for c in range(3):
                    roi_pano[mask, c] = (
                        roi_pano[mask, c] * roi_weight[mask] + 
                        roi_img[mask, c] * weight[mask]
                    ) / total_weight[mask]
                
                weight_map[y_offset:y_end, x_offset:x_end] = total_weight
                
                print(f"  融合图像 {i+1}: 位置 ({x_offset}, {y_offset})")
    
    return panorama

def improved_stitching(data_dir, output_dir="improved_panorama", config_mode="default", size=1000):
    """改进的拼接算法主函数"""
    
    print("🚀 开始改进的图像拼接...")
    
    # 获取图像列表
    valid_exts = {".jpg", ".png", ".bmp", ".jpeg"}
    image_paths = sorted([
        p for p in data_dir.iterdir()
        if p.suffix.lower() in valid_exts
    ], key=lambda p: natural_sort_key(str(p)))
    
    print(f"📸 找到 {len(image_paths)} 张图像")
    
    # 加载图像
    images = [Image(str(path), size) for path in image_paths]
    
    # 获取配置
    config = get_config(config_mode)
    Image.sfd2_extractor = Image.sfd2_extractor.__class__(config['sfd2'])
    
    # 创建匹配器
    matcher = IMPMatcher(
        match_threshold=config['imp']['match_threshold'],
        max_keypoints=config['imp']['max_keypoints']
    )
    
    # 收集匹配数据
    print("🔍 收集匹配数据...")
    matches_data = []
    
    for i in range(len(images) - 1):
        image_a = images[i]
        image_b = images[i + 1]
        
        # 提取特征
        image_a.compute_features()
        image_b.compute_features()
        
        # 匹配
        mkpts0, mkpts1 = matcher.match(image_a, image_b)
        
        if len(mkpts0) >= 4:
            # 计算单应性矩阵
            H, status = cv2.findHomography(mkpts1, mkpts0, cv2.RANSAC, 5.0, maxIters=500)
            
            matches_data.append({
                'image_indices': (i, i + 1),
                'homography': H,
                'inliers': np.sum(status) if status is not None else 0,
                'total_matches': len(mkpts0)
            })
            
            print(f"  {Path(image_a.path).stem} → {Path(image_b.path).stem}: "
                  f"{np.sum(status) if status is not None else 0}/{len(mkpts0)} 内点")
        else:
            print(f"  {Path(image_a.path).stem} → {Path(image_b.path).stem}: 匹配失败")
    
    # 估计相机参数
    camera_angles = estimate_camera_parameters(images, matches_data)
    
    print(f"📐 相机角度范围: {np.degrees(min(camera_angles)):.1f}° 到 {np.degrees(max(camera_angles)):.1f}°")
    
    # 创建柱面投影
    projected_images = create_cylindrical_projection(images, camera_angles)
    
    # 融合图像
    panorama = simple_blending_improved(projected_images, camera_angles)
    
    # 保存结果
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    result_path = output_path / "improved_panorama.jpg"
    cv2.imwrite(str(result_path), panorama)
    
    print(f"✅ 改进的全景图保存在: {result_path}")
    
    return panorama

def main():
    parser = argparse.ArgumentParser(description="改进的图像拼接")
    parser.add_argument("data_dir", type=Path, help="图像目录")
    parser.add_argument("--output", type=str, default="improved_panorama", help="输出目录")
    parser.add_argument("--config", type=str, default="default", help="配置模式")
    parser.add_argument("--size", type=int, default=1000, help="图像缩放尺寸")
    
    args = parser.parse_args()
    
    improved_stitching(args.data_dir, args.output, args.config, args.size)

if __name__ == "__main__":
    main()
