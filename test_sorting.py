#!/usr/bin/env python3
"""
测试文件名排序逻辑
"""

import re
from pathlib import Path

def natural_sort_key(path_str: str):
    """
    生成用于自然排序的键，使得数字按数值大小排序而不是字符串排序
    例如: 1.bmp, 2.bmp, 10.bmp 而不是 1.bmp, 10.bmp, 2.bmp
    """
    filename = Path(path_str).stem  # 获取不带扩展名的文件名
    # 将文件名中的数字部分转换为整数进行排序
    parts = re.split(r'(\d+)', filename)
    result = []
    for part in parts:
        if part.isdigit():
            result.append(int(part))
        else:
            result.append(part)
    return result

def test_sorting():
    """测试不同文件名的排序效果"""
    
    # 测试用例1: 纯数字文件名
    test_files_1 = [
        "10.bmp", "1.bmp", "2.bmp", "100.bmp", "3.bmp", "20.bmp", "0.bmp"
    ]
    
    print("测试用例1: 纯数字文件名")
    print("原始顺序:", test_files_1)
    
    # 字符串排序（错误的方式）
    string_sorted = sorted(test_files_1)
    print("字符串排序:", string_sorted)
    
    # 自然排序（正确的方式）
    natural_sorted = sorted(test_files_1, key=natural_sort_key)
    print("自然排序:", natural_sorted)
    print()
    
    # 测试用例2: 带前缀的文件名
    test_files_2 = [
        "img_10.jpg", "img_1.jpg", "img_2.jpg", "img_100.jpg", "img_3.jpg"
    ]
    
    print("测试用例2: 带前缀的文件名")
    print("原始顺序:", test_files_2)
    print("字符串排序:", sorted(test_files_2))
    print("自然排序:", sorted(test_files_2, key=natural_sort_key))
    print()
    
    # 测试用例3: 复杂文件名
    test_files_3 = [
        "frame_001_part_2.png", "frame_001_part_10.png", "frame_001_part_1.png",
        "frame_010_part_1.png", "frame_002_part_1.png"
    ]
    
    print("测试用例3: 复杂文件名")
    print("原始顺序:", test_files_3)
    print("字符串排序:", sorted(test_files_3))
    print("自然排序:", sorted(test_files_3, key=natural_sort_key))
    print()
    
    # 验证排序键的生成
    print("排序键示例:")
    for filename in ["0.bmp", "1.bmp", "10.bmp", "100.bmp"]:
        key = natural_sort_key(filename)
        print(f"  {filename} -> {key}")

if __name__ == "__main__":
    test_sorting()
