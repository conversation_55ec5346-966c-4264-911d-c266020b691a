#!/usr/bin/env python3
"""
调试特定图像对匹配问题的脚本
"""

import cv2
import numpy as np
import argparse
from pathlib import Path

from src.images import Image
from src.matching.imp_matcher import IMPMatcher
from src.matching.pair_match import PairMatch
from src.config import get_config, print_config

def debug_image_pair(image_path_a, image_path_b, config_mode="default", size=1000):
    """
    调试两张图像之间的匹配问题
    
    Args:
        image_path_a: 第一张图像路径
        image_path_b: 第二张图像路径
        config_mode: 配置模式
        size: 图像缩放尺寸
    """
    print(f"🔍 调试图像对匹配:")
    print(f"   图像A: {Path(image_path_a).name}")
    print(f"   图像B: {Path(image_path_b).name}")
    print(f"   配置模式: {config_mode}")
    print(f"   图像尺寸: {size}")
    print("-" * 60)
    
    # 获取配置
    config = get_config(config_mode)
    print_config(config)
    print()
    
    # 应用配置
    Image.sfd2_extractor = Image.sfd2_extractor.__class__(config['sfd2'])
    
    # 加载图像
    print("📸 加载图像...")
    try:
        image_a = Image(image_path_a, size)
        image_b = Image(image_path_b, size)
        print(f"   图像A尺寸: {image_a.image.shape}")
        print(f"   图像B尺寸: {image_b.image.shape}")
    except Exception as e:
        print(f"❌ 图像加载失败: {e}")
        return
    
    # 提取特征
    print("\n🔧 提取特征...")
    try:
        image_a.compute_features()
        image_b.compute_features()
        print(f"   图像A特征点数: {len(image_a.keypoints)}")
        print(f"   图像B特征点数: {len(image_b.keypoints)}")
        print(f"   图像A描述符维度: {image_a.features.shape}")
        print(f"   图像B描述符维度: {image_b.features.shape}")
    except Exception as e:
        print(f"❌ 特征提取失败: {e}")
        return
    
    # 创建匹配器并进行匹配
    print(f"\n🎯 进行特征匹配...")
    try:
        matcher = IMPMatcher(
            match_threshold=config['imp']['match_threshold'],
            max_keypoints=config['imp']['max_keypoints']
        )
        
        # 获取原始匹配结果
        mkpts0, mkpts1 = matcher.match(image_a, image_b)
        print(f"   原始匹配点数: {len(mkpts0)}")
        
        if len(mkpts0) == 0:
            print("❌ 没有找到任何匹配点!")
            return
        
        # 转换为DMatch格式
        matches = []
        for i in range(len(mkpts0)):
            # 找到匹配点在原始keypoints中的索引
            distances_a = np.linalg.norm(image_a.keypoints - mkpts0[i], axis=1)
            distances_b = np.linalg.norm(image_b.keypoints - mkpts1[i], axis=1)
            
            idx_a = np.argmin(distances_a)
            idx_b = np.argmin(distances_b)
            
            if distances_a[idx_a] < 1e-6 and distances_b[idx_b] < 1e-6:
                match = cv2.DMatch(idx_a, idx_b, 0.0)
                matches.append(match)
        
        print(f"   转换后匹配数: {len(matches)}")
        
    except Exception as e:
        print(f"❌ 特征匹配失败: {e}")
        return
    
    # 创建PairMatch对象进行验证
    print(f"\n✅ 验证匹配质量...")
    try:
        pair_match = PairMatch(image_a, image_b, matches)
        
        # 手动计算匹配点
        if hasattr(image_a.keypoints[0], 'pt'):
            # OpenCV KeyPoint格式
            matchpoints_a = np.float32([image_a.keypoints[m.queryIdx].pt for m in matches])
            matchpoints_b = np.float32([image_b.keypoints[m.trainIdx].pt for m in matches])
        else:
            # numpy数组格式
            matchpoints_a = np.float32([image_a.keypoints[m.queryIdx] for m in matches])
            matchpoints_b = np.float32([image_b.keypoints[m.trainIdx] for m in matches])
        
        print(f"   匹配点A形状: {matchpoints_a.shape}")
        print(f"   匹配点B形状: {matchpoints_b.shape}")
        
        if len(matchpoints_a) < 4:
            print(f"❌ 匹配点不足4个，无法计算单应性矩阵")
            return
        
        # 计算单应性矩阵
        H, status = cv2.findHomography(
            matchpoints_b, matchpoints_a, 
            cv2.RANSAC, 5.0, maxIters=500
        )
        
        if H is not None:
            inliers = np.sum(status)
            inlier_ratio = inliers / len(matches)
            print(f"   单应性矩阵: 成功计算")
            print(f"   内点数量: {inliers}/{len(matches)}")
            print(f"   内点比例: {inlier_ratio:.3f}")
            
            # 检查是否通过验证
            is_valid = pair_match.is_valid()
            print(f"   匹配验证: {'✅ 通过' if is_valid else '❌ 失败'}")
            
            if not is_valid:
                print(f"   失败原因: 内点数量不足或重叠区域太小")
                
                # 尝试更宽松的验证参数
                print(f"\n🔄 尝试更宽松的验证参数...")
                is_valid_loose = pair_match.is_valid(alpha=4, beta=0.1)
                print(f"   宽松验证: {'✅ 通过' if is_valid_loose else '❌ 仍然失败'}")
        else:
            print(f"❌ 单应性矩阵计算失败")
            
    except Exception as e:
        print(f"❌ 匹配验证失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    parser = argparse.ArgumentParser(description="调试图像对匹配问题")
    parser.add_argument("image_a", type=str, help="第一张图像路径")
    parser.add_argument("image_b", type=str, help="第二张图像路径")
    parser.add_argument("--config", type=str, default="default", 
                       choices=["default", "fast", "loose", "ultra_loose", "high_quality"],
                       help="配置模式")
    parser.add_argument("--size", type=int, default=1000, help="图像缩放尺寸")
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not Path(args.image_a).exists():
        print(f"❌ 图像A不存在: {args.image_a}")
        return
    
    if not Path(args.image_b).exists():
        print(f"❌ 图像B不存在: {args.image_b}")
        return
    
    debug_image_pair(args.image_a, args.image_b, args.config, args.size)

if __name__ == "__main__":
    main()
