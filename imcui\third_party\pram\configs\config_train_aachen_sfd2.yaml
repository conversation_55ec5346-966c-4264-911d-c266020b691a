dataset: [ 'Aachen' ]

network_: "segnet"
network: "segnetvit"
local_rank: 0
gpu: [ 0 ]

feature: "sfd2"
save_path: '/scratches/flyer_2/fx221/exp/pram'
landmark_path: "/scratches/flyer_3/fx221/exp/pram/landmarks/sfd2-gml"
dataset_path: "/scratches/flyer_3/fx221/dataset"

config_path: 'configs/datasets'

image_dim: 3
feat_dim: 128

min_inliers: 32
max_inliers: 512
random_inliers: true
max_keypoints: 1024
ignore_index: -1
output_dim: 1024
output_dim_: 2048
jitter_params:
  brightness: 0.5
  contrast: 0.5
  saturation: 0.25
  hue: 0.15
  blur: 0

scale_params: [ 0.5, 1.0 ]
pre_load: false
do_eval: true
train: true
inlier_th: 0.5
lr: 0.0001
min_lr: 0.00001
optimizer: "adam"
seg_loss: "cew"
seg_loss_nx: "cei"
cls_loss: "ce"
cls_loss_: "bce"
ac_fn: "relu"
norm_fn: "bn"
workers: 8
layers: 15
log_intervals: 50
eval_n_epoch: 10

use_mid_feature: true
norm_desc: false
with_sc: false
with_cls: true
with_score: false
with_aug: true
with_dist: true

batch_size: 32
its_per_epoch: 1000
decay_rate: 0.999992
decay_iter: 80000
epochs: 800

cluster_method: 'birch'

weight_path: null
weight_path_1: '20230719_221442_segnet_L15_A_resnet4x_B32_K1024_relu_bn_od1024_nc513_adamw_cew_md_A_birch/segnet.899.pth'
weight_path_2: '20240211_142623_segnetvit_L15_A_resnet4x_B32_K1024_relu_bn_od1024_nc513_adam_cew_md_A_birch/segnetvit.799.pth'
resume_path: null

n_class: 513

eval_max_keypoints: 4096

localization:
  loc_scene_name: [ ]
  save_path: '/scratches/flyer_2/fx221/exp/localizer/loc_results'
  seg_k: 10
  threshold: 12
  min_kpts: 256
  min_matches: 8
  min_inliers: 128
  matching_method_: "mnn"
  matching_method_1: "spg"
  matching_method_2: "gm"
  matching_method: "gml"
  matching_method_4: "adagml"
  save: false
  show: true
  show_time: 1
  with_original: true
  with_extra: false
  max_vrf: 1
  with_compress: true
  semantic_matching: true
  refinement_method_: 'matching'
  refinement_method: 'projection'
  pre_filtering_th: 0.95
  do_refinement: true
  covisibility_frame: 50
  refinement_radius: 30
  refinement_nn_ratio: 0.9
  refinement_max_matches: 0
