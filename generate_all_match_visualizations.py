#!/usr/bin/env python3
"""
为所有相邻图像对生成完整的匹配可视化文件
"""

import cv2
import numpy as np
import json
from pathlib import Path
import argparse

from src.images import Image
from src.matching.imp_matcher import IMPMatcher
from src.matching.pair_match import PairMatch
from src.config import get_config
from src.matching.multi_images_matches import natural_sort_key

def draw_keypoints_on_image(image, keypoints, color=(0, 255, 0), radius=3):
    """在图像上绘制关键点"""
    img_with_kpts = image.copy()
    
    if hasattr(keypoints[0], 'pt'):
        # OpenCV KeyPoint格式
        for kpt in keypoints:
            cv2.circle(img_with_kpts, (int(kpt.pt[0]), int(kpt.pt[1])), radius, color, -1)
    else:
        # numpy数组格式
        for kpt in keypoints:
            cv2.circle(img_with_kpts, (int(kpt[0]), int(kpt[1])), radius, color, -1)
    
    return img_with_kpts

def create_match_visualization(image_a, kpts_a, image_b, kpts_b, matches, inlier_mask=None):
    """创建匹配可视化图像"""
    # 转换keypoints为OpenCV格式
    if not hasattr(kpts_a[0], 'pt'):
        kpts_a_cv = [cv2.KeyPoint(float(kpt[0]), float(kpt[1]), 1) for kpt in kpts_a]
        kpts_b_cv = [cv2.KeyPoint(float(kpt[0]), float(kpt[1]), 1) for kpt in kpts_b]
    else:
        kpts_a_cv = kpts_a
        kpts_b_cv = kpts_b
    
    if inlier_mask is not None:
        # 分别绘制内点和外点
        inlier_indices = [i for i, mask in enumerate(inlier_mask) if mask[0] == 1]
        outlier_indices = [i for i, mask in enumerate(inlier_mask) if mask[0] == 0]
        
        # 先绘制内点（绿色）
        img_matches = cv2.drawMatches(
            image_a, kpts_a_cv, image_b, kpts_b_cv,
            [matches[i] for i in inlier_indices],
            None, matchColor=(0, 255, 0), singlePointColor=(0, 255, 0),
            flags=cv2.DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS
        )
        
        # 再绘制外点（红色）
        if outlier_indices:
            img_matches = cv2.drawMatches(
                image_a, kpts_a_cv, image_b, kpts_b_cv,
                [matches[i] for i in outlier_indices],
                img_matches, matchColor=(0, 0, 255), singlePointColor=(0, 0, 255),
                flags=cv2.DrawMatchesFlags_DRAW_OVER_OUTIMG
            )
    else:
        # 绘制所有匹配（绿色）
        img_matches = cv2.drawMatches(
            image_a, kpts_a_cv, image_b, kpts_b_cv, matches,
            None, matchColor=(0, 255, 0), singlePointColor=(0, 255, 0),
            flags=cv2.DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS
        )
    
    return img_matches

def analyze_image_pair(image_a, image_b, config_mode, output_dir):
    """分析单个图像对并生成所有可视化文件"""
    
    pair_name = f"{Path(image_a.path).stem}_{Path(image_b.path).stem}"
    pair_dir = output_dir / pair_name
    pair_dir.mkdir(exist_ok=True)
    
    print(f"🔍 分析图像对: {pair_name}")
    
    # 获取配置并应用
    config = get_config(config_mode)
    Image.sfd2_extractor = Image.sfd2_extractor.__class__(config['sfd2'])
    
    # 提取特征
    image_a.compute_features()
    image_b.compute_features()
    
    print(f"   特征点数: A={len(image_a.keypoints)}, B={len(image_b.keypoints)}")
    
    # 1. 保存关键点可视化
    print("   📍 保存关键点可视化...")
    kpts_a_img = draw_keypoints_on_image(image_a.image, image_a.keypoints)
    kpts_b_img = draw_keypoints_on_image(image_b.image, image_b.keypoints)
    
    cv2.imwrite(str(pair_dir / "keypoints_a.jpg"), kpts_a_img)
    cv2.imwrite(str(pair_dir / "keypoints_b.jpg"), kpts_b_img)
    
    # 2. 进行特征匹配
    print("   🎯 进行特征匹配...")
    matcher = IMPMatcher(
        match_threshold=config['imp']['match_threshold'],
        max_keypoints=config['imp']['max_keypoints']
    )
    
    mkpts0, mkpts1 = matcher.match(image_a, image_b)
    print(f"   原始匹配点数: {len(mkpts0)}")
    
    if len(mkpts0) == 0:
        print("   ❌ 没有找到匹配点")
        return False
    
    # 转换为DMatch格式
    matches = []
    for i in range(len(mkpts0)):
        distances_a = np.linalg.norm(image_a.keypoints - mkpts0[i], axis=1)
        distances_b = np.linalg.norm(image_b.keypoints - mkpts1[i], axis=1)
        
        idx_a = np.argmin(distances_a)
        idx_b = np.argmin(distances_b)
        
        if distances_a[idx_a] < 1e-6 and distances_b[idx_b] < 1e-6:
            match = cv2.DMatch(idx_a, idx_b, 0.0)
            matches.append(match)
    
    print(f"   有效匹配数: {len(matches)}")
    
    # 3. 保存原始匹配可视化
    print("   💚 保存原始匹配...")
    raw_matches_img = create_match_visualization(
        image_a.image, image_a.keypoints,
        image_b.image, image_b.keypoints,
        matches
    )
    cv2.imwrite(str(pair_dir / "raw_matches.jpg"), raw_matches_img)
    
    if len(matches) < 4:
        print("   ❌ 匹配点不足4个，无法计算单应性矩阵")
        return False
    
    # 4. 计算单应性矩阵和RANSAC
    print("   🎲 计算RANSAC...")
    if hasattr(image_a.keypoints[0], 'pt'):
        pts_a = np.float32([image_a.keypoints[m.queryIdx].pt for m in matches])
        pts_b = np.float32([image_b.keypoints[m.trainIdx].pt for m in matches])
    else:
        pts_a = np.float32([image_a.keypoints[m.queryIdx] for m in matches])
        pts_b = np.float32([image_b.keypoints[m.trainIdx] for m in matches])
    
    H, status = cv2.findHomography(pts_b, pts_a, cv2.RANSAC, 5.0, maxIters=500)
    
    if H is None:
        print("   ❌ 单应性矩阵计算失败")
        return False
    
    inliers = np.sum(status)
    inlier_ratio = inliers / len(matches)
    print(f"   内点数: {inliers}/{len(matches)} ({inlier_ratio:.1%})")
    
    # 5. 保存RANSAC匹配可视化
    print("   ✅ 保存RANSAC匹配...")
    ransac_matches_img = create_match_visualization(
        image_a.image, image_a.keypoints,
        image_b.image, image_b.keypoints,
        matches, status
    )
    cv2.imwrite(str(pair_dir / "ransac_matches.jpg"), ransac_matches_img)
    
    # 6. 保存变形图像
    print("   🔄 保存变形图像...")
    warped_b = cv2.warpPerspective(
        image_b.image, H, 
        (image_a.image.shape[1], image_a.image.shape[0])
    )
    cv2.imwrite(str(pair_dir / "warped_image_b.jpg"), warped_b)
    
    # 7. 保存叠加图像
    print("   🎨 保存叠加图像...")
    overlay = cv2.addWeighted(image_a.image, 0.5, warped_b, 0.5, 0)
    cv2.imwrite(str(pair_dir / "overlay.jpg"), overlay)
    
    # 8. 创建验证结果
    pair_match = PairMatch(image_a, image_b, matches)
    is_valid_default = pair_match.is_valid()
    is_valid_loose = pair_match.is_valid(alpha=4, beta=0.1)
    is_valid_extreme = pair_match.is_valid(alpha=2, beta=0.05)
    
    # 9. 保存统计信息
    print("   📊 保存统计信息...")
    stats = {
        "pair_name": pair_name,
        "image_a": {
            "path": str(image_a.path),
            "shape": list(image_a.image.shape),
            "keypoints_count": len(image_a.keypoints)
        },
        "image_b": {
            "path": str(image_b.path),
            "shape": list(image_b.image.shape),
            "keypoints_count": len(image_b.keypoints)
        },
        "matching": {
            "raw_matches_count": len(mkpts0),
            "valid_matches_count": len(matches),
            "inliers_count": int(inliers),
            "outliers_count": len(matches) - int(inliers),
            "inlier_ratio": float(inlier_ratio),
            "homography_found": True,
            "config_mode": config_mode
        },
        "validation": {
            "default_validation": bool(is_valid_default),
            "loose_validation": bool(is_valid_loose),
            "extreme_validation": bool(is_valid_extreme)
        },
        "config": config
    }
    
    with open(pair_dir / "statistics.json", 'w', encoding='utf-8') as f:
        json.dump(stats, f, indent=2, ensure_ascii=False)
    
    # 10. 保存验证详情
    validation_result = {
        "default_validation": bool(is_valid_default),
        "loose_validation": bool(is_valid_loose),
        "extreme_validation": bool(is_valid_extreme),
        "validation_params": {
            "default": {"alpha": 8, "beta": 0.3},
            "loose": {"alpha": 4, "beta": 0.1},
            "extreme": {"alpha": 2, "beta": 0.05}
        },
        "matching_quality": {
            "inlier_ratio": float(inlier_ratio),
            "quality_level": (
                "excellent" if inlier_ratio > 0.7 else
                "good" if inlier_ratio > 0.5 else
                "poor" if inlier_ratio > 0.3 else
                "very_poor"
            )
        }
    }
    
    with open(pair_dir / "validation.json", 'w', encoding='utf-8') as f:
        json.dump(validation_result, f, indent=2, ensure_ascii=False)
    
    print(f"   ✅ 完成分析: {pair_dir}")
    return True

def main():
    parser = argparse.ArgumentParser(description="为所有相邻图像对生成匹配可视化")
    parser.add_argument("data_dir", type=Path, help="图像目录")
    parser.add_argument("--output", type=str, default="all_match_analysis", help="输出目录")
    parser.add_argument("--config", type=str, default="default", 
                       choices=["default", "fast", "loose", "ultra_loose", "high_quality", "super_high_quality"],
                       help="配置模式")
    parser.add_argument("--size", type=int, default=1000, help="图像缩放尺寸")
    
    args = parser.parse_args()
    
    # 获取图像列表
    valid_exts = {".jpg", ".png", ".bmp", ".jpeg"}
    image_paths = sorted([
        p for p in args.data_dir.iterdir()
        if p.suffix.lower() in valid_exts
    ], key=lambda p: natural_sort_key(str(p)))
    
    if len(image_paths) < 2:
        print("❌ 需要至少两张图像")
        return
    
    print(f"📸 找到 {len(image_paths)} 张图像")
    print(f"🔧 使用配置: {args.config}")
    print(f"📁 输出目录: {args.output}")
    print(f"🎯 将分析 {len(image_paths)-1} 对相邻图像")
    
    # 创建输出目录
    output_dir = Path(args.output)
    output_dir.mkdir(exist_ok=True)
    
    # 加载图像
    images = [Image(str(path), args.size) for path in image_paths]
    
    # 分析所有相邻图像对
    success_count = 0
    total_pairs = len(images) - 1
    
    for i in range(total_pairs):
        image_a = images[i]
        image_b = images[i + 1]
        
        print(f"\n[{i+1}/{total_pairs}] 处理图像对:")
        print(f"  A: {Path(image_a.path).name}")
        print(f"  B: {Path(image_b.path).name}")
        
        try:
            success = analyze_image_pair(image_a, image_b, args.config, output_dir)
            if success:
                success_count += 1
        except Exception as e:
            print(f"  ❌ 分析失败: {e}")
    
    print(f"\n🎉 完成！")
    print(f"📊 成功分析: {success_count}/{total_pairs} 对图像")
    print(f"📁 结果保存在: {output_dir.absolute()}")
    print(f"\n📋 每个图像对包含以下文件:")
    print(f"  • keypoints_a.jpg / keypoints_b.jpg - 关键点可视化")
    print(f"  • raw_matches.jpg - 原始匹配（绿色线条）")
    print(f"  • ransac_matches.jpg - RANSAC匹配（绿色=内点，红色=外点）")
    print(f"  • warped_image_b.jpg - 变形后的第二张图像")
    print(f"  • overlay.jpg - 两张图像的叠加效果")
    print(f"  • statistics.json - 详细统计信息")
    print(f"  • validation.json - 验证结果")

if __name__ == "__main__":
    main()
