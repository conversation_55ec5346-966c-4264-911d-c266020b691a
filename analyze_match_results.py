#!/usr/bin/env python3
"""
分析匹配结果的汇总报告
"""

import json
from pathlib import Path

def analyze_match_results(analysis_dir="match_analysis_testdata2"):
    """分析匹配结果目录中的所有统计信息"""
    
    analysis_path = Path(analysis_dir)
    if not analysis_path.exists():
        print(f"❌ 分析目录不存在: {analysis_dir}")
        return
    
    print("📊 图像匹配质量分析报告")
    print("=" * 60)
    
    # 收集所有统计信息
    all_stats = []
    
    for pair_dir in analysis_path.iterdir():
        if pair_dir.is_dir():
            stats_file = pair_dir / "statistics.json"
            validation_file = pair_dir / "validation.json"
            
            if stats_file.exists() and validation_file.exists():
                with open(stats_file, 'r', encoding='utf-8') as f:
                    stats = json.load(f)
                
                with open(validation_file, 'r', encoding='utf-8') as f:
                    validation = json.load(f)
                
                # 合并数据
                combined = {
                    "pair_name": stats["pair_name"],
                    "keypoints_a": stats["image_a"]["keypoints_count"],
                    "keypoints_b": stats["image_b"]["keypoints_count"],
                    "raw_matches": stats["matching"]["raw_matches_count"],
                    "inliers": stats["matching"]["inliers_count"],
                    "outliers": stats["matching"]["outliers_count"],
                    "inlier_ratio": stats["matching"]["inlier_ratio"],
                    "default_valid": validation["default_validation"],
                    "loose_valid": validation["loose_validation"],
                    "extreme_valid": validation["extreme_validation"]
                }
                
                all_stats.append(combined)
    
    # 按图像对名称排序
    all_stats.sort(key=lambda x: x["pair_name"])
    
    # 打印详细表格
    print(f"{'图像对':<12} {'特征点A':<8} {'特征点B':<8} {'原始匹配':<8} {'内点':<6} {'外点':<6} {'内点率':<8} {'默认验证':<8} {'宽松验证':<8} {'极端验证':<8}")
    print("-" * 100)
    
    total_pairs = len(all_stats)
    valid_default = 0
    valid_loose = 0
    valid_extreme = 0
    
    quality_categories = {
        "excellent": [],  # 内点率 > 70%
        "good": [],       # 内点率 50-70%
        "poor": [],       # 内点率 30-50%
        "very_poor": []   # 内点率 < 30%
    }
    
    for stats in all_stats:
        # 统计验证通过数
        if stats["default_valid"]:
            valid_default += 1
        if stats["loose_valid"]:
            valid_loose += 1
        if stats["extreme_valid"]:
            valid_extreme += 1
        
        # 分类质量
        inlier_ratio = stats["inlier_ratio"]
        if inlier_ratio > 0.7:
            quality_categories["excellent"].append(stats["pair_name"])
        elif inlier_ratio > 0.5:
            quality_categories["good"].append(stats["pair_name"])
        elif inlier_ratio > 0.3:
            quality_categories["poor"].append(stats["pair_name"])
        else:
            quality_categories["very_poor"].append(stats["pair_name"])
        
        # 打印行
        print(f"{stats['pair_name']:<12} "
              f"{stats['keypoints_a']:<8} "
              f"{stats['keypoints_b']:<8} "
              f"{stats['raw_matches']:<8} "
              f"{stats['inliers']:<6} "
              f"{stats['outliers']:<6} "
              f"{stats['inlier_ratio']:<8.1%} "
              f"{'✅' if stats['default_valid'] else '❌':<8} "
              f"{'✅' if stats['loose_valid'] else '❌':<8} "
              f"{'✅' if stats['extreme_valid'] else '❌':<8}")
    
    # 汇总统计
    print("\n" + "=" * 60)
    print("📈 汇总统计")
    print("=" * 60)
    
    print(f"总图像对数: {total_pairs}")
    print(f"默认验证通过: {valid_default}/{total_pairs} ({valid_default/total_pairs:.1%})")
    print(f"宽松验证通过: {valid_loose}/{total_pairs} ({valid_loose/total_pairs:.1%})")
    print(f"极端验证通过: {valid_extreme}/{total_pairs} ({valid_extreme/total_pairs:.1%})")
    
    print(f"\n📊 匹配质量分布:")
    print(f"优秀 (>70%内点率): {len(quality_categories['excellent'])} 对")
    if quality_categories['excellent']:
        print(f"  {', '.join(quality_categories['excellent'])}")
    
    print(f"良好 (50-70%内点率): {len(quality_categories['good'])} 对")
    if quality_categories['good']:
        print(f"  {', '.join(quality_categories['good'])}")
    
    print(f"较差 (30-50%内点率): {len(quality_categories['poor'])} 对")
    if quality_categories['poor']:
        print(f"  {', '.join(quality_categories['poor'])}")
    
    print(f"很差 (<30%内点率): {len(quality_categories['very_poor'])} 对")
    if quality_categories['very_poor']:
        print(f"  {', '.join(quality_categories['very_poor'])}")
    
    # 问题诊断
    print(f"\n🔍 问题诊断:")
    
    failed_default = [s for s in all_stats if not s["default_valid"]]
    if failed_default:
        print(f"默认验证失败的图像对 ({len(failed_default)}个):")
        for stats in failed_default:
            print(f"  {stats['pair_name']}: {stats['inliers']}内点/{stats['raw_matches']}匹配 ({stats['inlier_ratio']:.1%})")
    
    low_matches = [s for s in all_stats if s["raw_matches"] < 20]
    if low_matches:
        print(f"\n原始匹配数过少的图像对 (<20个):")
        for stats in low_matches:
            print(f"  {stats['pair_name']}: {stats['raw_matches']}个匹配")
    
    low_inliers = [s for s in all_stats if s["inlier_ratio"] < 0.3]
    if low_inliers:
        print(f"\n内点率过低的图像对 (<30%):")
        for stats in low_inliers:
            print(f"  {stats['pair_name']}: {stats['inlier_ratio']:.1%} ({stats['inliers']}/{stats['raw_matches']})")
    
    # 建议
    print(f"\n💡 优化建议:")
    
    if len(quality_categories['very_poor']) > 0:
        print(f"1. 对于内点率很低的图像对，考虑:")
        print(f"   - 使用更宽松的匹配阈值")
        print(f"   - 增加特征点数量")
        print(f"   - 检查图像重叠区域是否足够")
    
    if len(low_matches) > 0:
        print(f"2. 对于原始匹配数过少的图像对，考虑:")
        print(f"   - 降低IMP匹配阈值")
        print(f"   - 增加SFD2特征点数量")
        print(f"   - 降低SFD2特征点阈值")
    
    avg_inlier_ratio = sum(s["inlier_ratio"] for s in all_stats) / len(all_stats)
    print(f"\n📊 平均内点率: {avg_inlier_ratio:.1%}")
    
    if avg_inlier_ratio < 0.5:
        print(f"⚠️  整体匹配质量偏低，建议调整参数或检查图像质量")
    elif avg_inlier_ratio > 0.7:
        print(f"✅ 整体匹配质量良好")
    else:
        print(f"🔧 整体匹配质量中等，有优化空间")

if __name__ == "__main__":
    analyze_match_results()
