#!/usr/bin/env python3
"""
性能对比脚本：比较全匹配和顺序匹配的速度差异
"""

import argparse
import time
import logging
from pathlib import Path

import cv2
import numpy as np

from src.images import Image
from src.matching.imp_matcher import IMPMatcher
from src.matching import PairMatch, MultiImageMatches

def benchmark_matching_strategies(data_dir: Path, size: int = None):
    """
    对比不同匹配策略的性能
    
    Args:
        data_dir: 图像目录
        size: 图像缩放尺寸
    """
    # 支持的图像扩展名
    valid_exts = {".jpg", ".png", ".bmp", ".jpeg"}
    image_paths = sorted([
        str(p) for p in data_dir.iterdir()
        if p.suffix.lower() in valid_exts
    ])

    if len(image_paths) < 2:
        print("需要至少两张图像进行测试")
        return

    print(f"找到 {len(image_paths)} 张图像")
    print("图像列表:")
    for i, path in enumerate(image_paths):
        print(f"  {i}: {Path(path).name}")
    print()

    # 加载图像并提取特征
    print("加载图像并提取特征...")
    start_time = time.time()
    images = [Image(path, size) for path in image_paths]
    for img in images:
        img.compute_features()
    feature_time = time.time() - start_time
    print(f"特征提取耗时: {feature_time:.2f}s")
    print()

    # 创建匹配器
    matcher = IMPMatcher()

    # 测试1: 全匹配策略
    print("=" * 50)
    print("测试1: 全匹配策略 (All-to-All)")
    print("=" * 50)
    start_time = time.time()
    
    match_graph_all = MultiImageMatches(images, matcher, sequential_matching=False)
    pair_matches_all = match_graph_all.get_pair_matches()
    
    all_matching_time = time.time() - start_time
    print(f"全匹配耗时: {all_matching_time:.2f}s")
    print(f"找到 {len(pair_matches_all)} 个有效匹配对")
    
    # 显示匹配详情
    for pm in pair_matches_all:
        img_a_name = Path(pm.image_a.path).name
        img_b_name = Path(pm.image_b.path).name
        print(f"  {img_a_name} <-> {img_b_name}: {len(pm.matches)} 个特征匹配")
    print()

    # 测试2: 顺序匹配策略 (range=1)
    print("=" * 50)
    print("测试2: 顺序匹配策略 (Sequential, range=1)")
    print("=" * 50)
    start_time = time.time()
    
    match_graph_seq1 = MultiImageMatches(images, matcher, sequential_matching=True, seq_range=1)
    pair_matches_seq1 = match_graph_seq1.get_pair_matches()
    
    seq1_matching_time = time.time() - start_time
    print(f"顺序匹配(range=1)耗时: {seq1_matching_time:.2f}s")
    print(f"找到 {len(pair_matches_seq1)} 个有效匹配对")
    print()

    # 测试3: 顺序匹配策略 (range=2)
    print("=" * 50)
    print("测试3: 顺序匹配策略 (Sequential, range=2)")
    print("=" * 50)
    start_time = time.time()
    
    match_graph_seq2 = MultiImageMatches(images, matcher, sequential_matching=True, seq_range=2)
    pair_matches_seq2 = match_graph_seq2.get_pair_matches()
    
    seq2_matching_time = time.time() - start_time
    print(f"顺序匹配(range=2)耗时: {seq2_matching_time:.2f}s")
    print(f"找到 {len(pair_matches_seq2)} 个有效匹配对")
    print()

    # 性能总结
    print("=" * 50)
    print("性能总结")
    print("=" * 50)
    print(f"特征提取耗时:        {feature_time:.2f}s")
    print(f"全匹配耗时:          {all_matching_time:.2f}s")
    print(f"顺序匹配(range=1)耗时: {seq1_matching_time:.2f}s")
    print(f"顺序匹配(range=2)耗时: {seq2_matching_time:.2f}s")
    print()
    print("速度提升:")
    if all_matching_time > 0:
        speedup_seq1 = all_matching_time / seq1_matching_time
        speedup_seq2 = all_matching_time / seq2_matching_time
        print(f"顺序匹配(range=1) vs 全匹配: {speedup_seq1:.1f}x 更快")
        print(f"顺序匹配(range=2) vs 全匹配: {speedup_seq2:.1f}x 更快")
    print()
    
    print("匹配对数量:")
    print(f"全匹配:            {len(pair_matches_all)} 对")
    print(f"顺序匹配(range=1):  {len(pair_matches_seq1)} 对")
    print(f"顺序匹配(range=2):  {len(pair_matches_seq2)} 对")
    print()
    
    # 理论复杂度分析
    n = len(images)
    theoretical_all = n * (n - 1) // 2  # 全匹配的理论匹配对数
    theoretical_seq1 = n - 1  # 顺序匹配(range=1)的理论匹配对数
    theoretical_seq2 = min(n * 2 - 3, theoretical_all)  # 顺序匹配(range=2)的理论匹配对数
    
    print("理论复杂度分析:")
    print(f"图像数量: {n}")
    print(f"全匹配理论匹配对数:      {theoretical_all}")
    print(f"顺序匹配(range=1)理论对数: {theoretical_seq1}")
    print(f"顺序匹配(range=2)理论对数: {theoretical_seq2}")
    print(f"复杂度降低: O(n²) -> O(n)")

def main():
    parser = argparse.ArgumentParser(description="图像匹配策略性能对比")
    parser.add_argument("data_dir", type=Path, help="图像目录")
    parser.add_argument("--size", type=int, default=None, help="图像缩放尺寸")
    
    args = parser.parse_args()
    
    if not args.data_dir.exists():
        print(f"错误: 目录 {args.data_dir} 不存在")
        return
    
    benchmark_matching_strategies(args.data_dir, args.size)

if __name__ == "__main__":
    main()
