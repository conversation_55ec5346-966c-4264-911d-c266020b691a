import cv2
import numpy as np

# 1. 读取图像
img1 = cv2.imread("D:/daima/gitup/7month/image-stitching-main/testdata3/2.bmp")
img2 = cv2.imread("D:/daima/gitup/7month/image-stitching-main/testdata3/4.bmp")

# 2. 从匹配结果中填入 Homography 矩阵
H = np.array([
    [1.118417764482483, 0.0603628696196226, 948.2571340071909],
    [0.08647869143597661, 1.1855958767837127, -14.843825929155376],
    [0.000005834151500282669, 0.00020521323622606978, 1]
])

# 3. 获取图像尺寸
h1, w1 = img1.shape[:2]
h2, w2 = img2.shape[:2]

# 4. 计算变换后图像1的四个角在图像2坐标系下的位置
corners_img1 = np.float32([[0,0], [w1,0], [w1,h1], [0,h1]]).reshape(-1,1,2)
transformed_corners = cv2.perspectiveTransform(corners_img1, H)

# 5. 计算拼接图像的边界
all_corners = np.concatenate((transformed_corners, np.float32([[0,0],[w2,0],[w2,h2],[0,h2]]).reshape(-1,1,2)), axis=0)
[x_min, y_min] = np.int32(all_corners.min(axis=0).ravel() - 0.5)
[x_max, y_max] = np.int32(all_corners.max(axis=0).ravel() + 0.5)

# 6. 平移矩阵，防止坐标为负
translate_dist = [-x_min, -y_min]
T = np.array([
    [1, 0, translate_dist[0]],
    [0, 1, translate_dist[1]],
    [0, 0, 1]
])

# 7. 图像1进行透视变换
result_img1 = cv2.warpPerspective(img1, T @ H, (x_max - x_min, y_max - y_min))

# 8. 图像2直接放到画布上
result_img2 = np.zeros_like(result_img1)
result_img2[translate_dist[1]:translate_dist[1]+h2, translate_dist[0]:translate_dist[0]+w2] = img2

# 9. 简单融合（可改为加权或金字塔融合）
mask = (result_img1 > 0)
result_img2[mask] = result_img1[mask]

# 10. 显示或保存结果
cv2.imshow("Stitched", result_img2)
cv2.waitKey(0)
cv2.destroyAllWindows()
# 也可以保存
cv2.imwrite("stitched_result.bmp", result_img2)
