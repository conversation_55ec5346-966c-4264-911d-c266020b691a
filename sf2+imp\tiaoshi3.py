from imcui.hloc.extractors.sfd2 import SFD2
from imcui.hloc.matchers.imp import IMP
import torch
import numpy as np
import cv2
import matplotlib.pyplot as plt
from PIL import Image
import os

# 加载 SFD2 特征提取器
sfd2_extractor = SFD2({'max_keypoints': 4096, 'conf_th': 0.001})

# 加载 IMP 特征匹配器
imp_matcher = IMP({'match_threshold': 0.2, 'features': 'sfd2'})

# 使用 PIL 加载灰度图像（支持中文路径），并可缩放
def load_gray_image_unicode(path, resize_scale=0.5):
    img = Image.open(path).convert('L')
    if resize_scale != 1.0:
        new_size = (int(img.width * resize_scale), int(img.height * resize_scale))
        img = img.resize(new_size, Image.BILINEAR)
    return np.array(img)

# 转为 [1, 3, H, W]，供模型输入
def preprocess_gray(image):
    image = np.stack([image]*3, axis=-1)  # 灰度转3通道
    return torch.from_numpy(image).float().permute(2, 0, 1).unsqueeze(0) / 255.0

# 拼接函数（带加权融合）
def stitch_images_with_blending(img1, img2, H):
    h1, w1 = img1.shape[:2]
    h2, w2 = img2.shape[:2]
    corners_img1 = np.float32([[0, 0], [0, h1], [w1, h1], [w1, 0]]).reshape(-1, 1, 2)
    corners_img1_trans = cv2.perspectiveTransform(corners_img1, H)
    corners_img2 = np.float32([[0, 0], [0, h2], [w2, h2], [w2, 0]]).reshape(-1, 1, 2)
    all_corners = np.concatenate((corners_img1_trans, corners_img2), axis=0)

    [xmin, ymin] = np.floor(np.min(all_corners, axis=0).ravel()).astype(int)
    [xmax, ymax] = np.ceil(np.max(all_corners, axis=0).ravel()).astype(int)
    translation = [-xmin, -ymin]
    output_size = (xmax - xmin, ymax - ymin)

    H_translation = np.array([[1, 0, translation[0]],
                              [0, 1, translation[1]],
                              [0, 0, 1]])

    warped_img1 = cv2.warpPerspective(img1, H_translation @ H, output_size)
    mask1 = (warped_img1 > 0).astype(np.float32)

    paste_x, paste_y = translation
    canvas_img2 = np.zeros_like(warped_img1, dtype=np.uint8)
    canvas_img2[paste_y:paste_y+h2, paste_x:paste_x+w2] = img2
    mask2 = (canvas_img2 > 0).astype(np.float32)

    # 融合权重：使用高斯模糊平滑mask实现柔和过渡
    weight1 = cv2.GaussianBlur(mask1, (51, 51), 0)
    weight2 = cv2.GaussianBlur(mask2, (51, 51), 0)
    total_weight = weight1 + weight2
    total_weight[total_weight == 0] = 1

    blended = (warped_img1.astype(np.float32) * weight1 + canvas_img2.astype(np.float32) * weight2) / total_weight
    blended = np.clip(blended, 0, 255).astype(np.uint8)

    return blended

# 图像文件夹和命名
folder = r'G:/photo_2D_80ns_A'
file_template = "{}.bmp"  # 文件命名格式，比如 1.bmp, 2.bmp ...

start_idx = 0  # 起始图像编号
end_idx = 6   # 结束编号

output_dir = os.path.join(folder, 'pinjie')
os.makedirs(output_dir, exist_ok=True)

for i in range(start_idx, end_idx):
    img_path0 = os.path.join(folder, file_template.format(i))
    img_path1 = os.path.join(folder, file_template.format(i + 1))

    print(f"正在拼接第 {i} 和第 {i+1} 张图片...")
    try:
        image0 = load_gray_image_unicode(img_path0, resize_scale=0.5)
        image1 = load_gray_image_unicode(img_path1, resize_scale=0.5)
    except Exception as e:
        print(f"读取图像失败: {img_path0} 或 {img_path1}，错误: {e}")
        continue

    image0_tensor = preprocess_gray(image0)
    image1_tensor = preprocess_gray(image1)

    pred0 = sfd2_extractor({'image': image0_tensor})
    pred1 = sfd2_extractor({'image': image1_tensor})

    matches = imp_matcher({
        'image0': image0_tensor,
        'keypoints0': pred0['keypoints'],
        'scores0': pred0['scores'],
        'descriptors0': pred0['descriptors'],
        'image1': image1_tensor,
        'keypoints1': pred1['keypoints'],
        'scores1': pred1['scores'],
        'descriptors1': pred1['descriptors'],
    })

    keypoints0 = pred0['keypoints'][0].cpu().numpy()
    keypoints1 = pred1['keypoints'][0].cpu().numpy()

    if 'matches0' in matches:
        matches0 = matches['matches0'][0].cpu().numpy()
        valid = matches0 > -1
        mkpts0 = keypoints0[valid]
        mkpts1 = keypoints1[matches0[valid]]
    else:
        print(f"{i}-{i+1}: 匹配失败")
        continue

    if len(mkpts0) >= 4:
        H, mask = cv2.findHomography(mkpts0, mkpts1, cv2.RANSAC, 3.0)
        inliers = mask.ravel().astype(bool)
        print(f"{i}-{i+1}: 内点数量: {np.sum(inliers)}")

        stitched = stitch_images_with_blending(image0, image1, H)

        out_path = os.path.join(output_dir, f'stitched_{i}_{i+1}.png')
        cv2.imwrite(out_path, stitched)
        print(f"{i}-{i+1}: 拼接完成，保存为 {out_path}")
    else:
        print(f"{i}-{i+1}: 匹配点不足，跳过拼接")
