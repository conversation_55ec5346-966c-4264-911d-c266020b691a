from imcui.hloc.extractors.sfd2 import SFD2
from imcui.hloc.matchers.imp import IMP
import torch
import numpy as np
import cv2
from PIL import Image
import os

# 加载 SFD2 特征提取器
sfd2_extractor = SFD2({'max_keypoints': 4096, 'conf_th': 0.001})

# 加载 IMP 特征匹配器
imp_matcher = IMP({'match_threshold': 0.2, 'features': 'sfd2'})

def load_gray_image_unicode(path, resize_scale=1.0):
    img = Image.open(path).convert('L')
    if resize_scale != 1.0:
        new_size = (int(img.width * resize_scale), int(img.height * resize_scale))
        img = img.resize(new_size, Image.BILINEAR)
    return np.array(img)

def preprocess_gray(image):
    image = np.stack([image]*3, axis=-1)
    return torch.from_numpy(image).float().permute(2, 0, 1).unsqueeze(0) / 255.0

def stitch_images_with_blending(img1, img2, H):
    h1, w1 = img1.shape[:2]
    h2, w2 = img2.shape[:2]

    corners_img1 = np.float32([[0, 0], [0, h1], [w1, h1], [w1, 0]]).reshape(-1, 1, 2)
    corners_img1_trans = cv2.perspectiveTransform(corners_img1, H)
    corners_img2 = np.float32([[0, 0], [0, h2], [w2, h2], [w2, 0]]).reshape(-1, 1, 2)

    all_corners = np.concatenate((corners_img1_trans, corners_img2), axis=0)

    [xmin, ymin] = np.floor(np.min(all_corners, axis=0).ravel()).astype(int)
    [xmax, ymax] = np.ceil(np.max(all_corners, axis=0).ravel()).astype(int)

    translation = [-xmin, -ymin]
    output_size = (xmax - xmin, ymax - ymin)

    H_translation = np.array([[1, 0, translation[0]],
                              [0, 1, translation[1]],
                              [0, 0, 1]])

    warped_img1 = cv2.warpPerspective(img1, H_translation @ H, output_size)
    mask1 = (warped_img1 > 0).astype(np.float32)

    paste_x, paste_y = translation
    canvas_img2 = np.zeros_like(warped_img1, dtype=np.uint8)
    canvas_img2[paste_y:paste_y+h2, paste_x:paste_x+w2] = img2
    mask2 = (canvas_img2 > 0).astype(np.float32)

    weight1 = cv2.GaussianBlur(mask1, (51, 51), 0)
    weight2 = cv2.GaussianBlur(mask2, (51, 51), 0)
    total_weight = weight1 + weight2
    total_weight[total_weight == 0] = 1

    blended = (warped_img1.astype(np.float32)*weight1 + canvas_img2.astype(np.float32)*weight2) / total_weight
    blended = np.clip(blended, 0, 255).astype(np.uint8)

    return blended

# 文件夹和命名
folder = r'G:/photo_2D_80ns_A'
file_template = "{}.bmp"

start_idx = 0
end_idx = 6

output_dir = os.path.join(folder, 'pinjie')
os.makedirs(output_dir, exist_ok=True)

# 初始化 base_image，读取第一张图，**不缩放**
base_image_path = os.path.join(folder, file_template.format(start_idx))
base_image = load_gray_image_unicode(base_image_path, resize_scale=1.0)

for i in range(start_idx + 1, end_idx + 1):
    next_image_path = os.path.join(folder, file_template.format(i))
    print(f"正在拼接累计图像与第 {i} 张图片：{next_image_path}")
    try:
        # 仅对新图缩放50%
        next_image = load_gray_image_unicode(next_image_path, resize_scale=0.5)
    except Exception as e:
        print(f"读取图像失败: {next_image_path}，错误: {e}")
        continue

    base_tensor = preprocess_gray(base_image)
    next_tensor = preprocess_gray(next_image)

    pred_base = sfd2_extractor({'image': base_tensor})
    pred_next = sfd2_extractor({'image': next_tensor})

    matches = imp_matcher({
        'image0': base_tensor,
        'keypoints0': pred_base['keypoints'],
        'scores0': pred_base['scores'],
        'descriptors0': pred_base['descriptors'],
        'image1': next_tensor,
        'keypoints1': pred_next['keypoints'],
        'scores1': pred_next['scores'],
        'descriptors1': pred_next['descriptors'],
    })

    keypoints_base = pred_base['keypoints'][0].cpu().numpy()
    keypoints_next = pred_next['keypoints'][0].cpu().numpy()

    if 'matches0' in matches:
        matches0 = matches['matches0'][0].cpu().numpy()
        valid = matches0 > -1
        mkpts_base = keypoints_base[valid]
        mkpts_next = keypoints_next[matches0[valid]]
    else:
        print(f"{i}: 匹配失败，跳过")
        continue

    if len(mkpts_base) >= 4:
        H, mask = cv2.findHomography(mkpts_base, mkpts_next, cv2.RANSAC, 3.0)
        if H is None:
            print(f"{i}: 计算单应矩阵失败，跳过")
            continue
        inliers = mask.ravel().astype(bool)
        print(f"{i}: 内点数量: {np.sum(inliers)}")

        base_image = stitch_images_with_blending(base_image, next_image, H)

        out_path = os.path.join(output_dir, f'cumulative_stitched_up_to_{i}.png')
        cv2.imwrite(out_path, base_image)
        print(f"已保存累计拼接图像: {out_path}")
    else:
        print(f"{i}: 匹配点不足，跳过拼接")

print("全部拼接完成！")
