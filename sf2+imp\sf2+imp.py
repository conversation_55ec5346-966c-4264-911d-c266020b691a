from imcui.hloc.extractors.sfd2 import SFD2
from imcui.hloc.matchers.imp import IMP
import cv2
import torch
import numpy as np
import matplotlib.pyplot as plt
import os

# 加载 SFD2 特征提取器
sfd2_extractor = SFD2({'max_keypoints': 4096, 'conf_th': 0.001})

# 加载 IMP 特征匹配器
imp_matcher = IMP({'match_threshold': 0.2, 'features': 'sfd2'})

# 读取图像路径
img_path0 = 'D:/aa_shanhu/output_frames2/101.jpg'
img_path1 = 'D:/aa_shanhu/output_frames2/102.jpg'

# 读取图像并转换为 RGB 格式
image0 = cv2.imread(img_path0)
image1 = cv2.imread(img_path1)

if image0 is None or image1 is None:
    raise FileNotFoundError("无法读取图像，请检查路径是否正确")

image0 = cv2.cvtColor(image0, cv2.COLOR_BGR2RGB)
image1 = cv2.cvtColor(image1, cv2.COLOR_BGR2RGB)

# 转换为 [1, 3, H, W] 并归一化到 [0, 1]
def preprocess(image):
    return torch.from_numpy(image).float().permute(2, 0, 1).unsqueeze(0) / 255.0

image0_tensor = preprocess(image0)
image1_tensor = preprocess(image1)

# 特征提取
pred0 = sfd2_extractor({'image': image0_tensor})
pred1 = sfd2_extractor({'image': image1_tensor})

# 特征匹配
matches = imp_matcher({
    'image0': image0_tensor,
    'keypoints0': pred0['keypoints'],
    'scores0': pred0['scores'],
    'descriptors0': pred0['descriptors'],
    'image1': image1_tensor,
    'keypoints1': pred1['keypoints'],
    'scores1': pred1['scores'],
    'descriptors1': pred1['descriptors'],
})

# 解析匹配结果
keypoints0 = pred0['keypoints'][0].cpu().numpy()
keypoints1 = pred1['keypoints'][0].cpu().numpy()

if 'matches0' in matches:
    matches0 = matches['matches0'][0].cpu().numpy()
    valid = matches0 > -1
    mkpts0 = keypoints0[valid]
    mkpts1 = keypoints1[matches0[valid]]
elif 'keypoints0' in matches and 'keypoints1' in matches:
    mkpts0 = matches['keypoints0'].cpu().numpy()
    mkpts1 = matches['keypoints1'].cpu().numpy()
else:
    print("未知匹配结果格式，返回键包含：", matches.keys())
    mkpts0 = np.array([])
    mkpts1 = np.array([])

print(f"共找到 {len(mkpts0)} 对匹配点")

# 计算单应性矩阵
if len(mkpts0) >= 4:
    H, mask = cv2.findHomography(mkpts0, mkpts1, cv2.RANSAC, 3.0)
    inliers = mask.ravel().astype(bool)
    inlier_mkpts0 = mkpts0[inliers]
    inlier_mkpts1 = mkpts1[inliers]
    print(f"内点数量: {np.sum(inliers)}")
else:
    print("匹配点不足，无法计算单应性矩阵")
    H = None
    inlier_mkpts0 = np.array([])
    inlier_mkpts1 = np.array([])

# 匹配可视化函数
def plot_matches(img1, img2, pts1, pts2, color=(0, 255, 0)):
    h1, w1 = img1.shape[:2]
    h2, w2 = img2.shape[:2]
    vis = np.zeros((max(h1, h2), w1 + w2, 3), dtype=np.uint8)
    vis[:h1, :w1] = img1
    vis[:h2, w1:w1 + w2] = img2
    for pt1, pt2 in zip(pts1, pts2):
        pt1 = tuple(map(int, pt1))
        pt2 = (int(pt2[0] + w1), int(pt2[1]))
        cv2.circle(vis, pt1, 3, color, -1)
        cv2.circle(vis, pt2, 3, color, -1)
        cv2.line(vis, pt1, pt2, color, 1)
    return vis

# 可视化结果并保存
if len(inlier_mkpts0) > 0:
    vis_img = plot_matches(image0, image1, inlier_mkpts0, inlier_mkpts1)
    plt.figure(figsize=(20, 10))
    plt.imshow(vis_img)
    plt.axis('off')
    plt.title(f'SFD2+IMP 匹配结果：{len(inlier_mkpts0)} 对内点（总匹配 {len(mkpts0)}）')
    plt.savefig('匹配结果_内点.png', bbox_inches='tight')
    plt.show()
    np.savez('匹配结果_内点.npz', 
             keypoints0=inlier_mkpts0, 
             keypoints1=inlier_mkpts1, 
             homography=H)
    print("匹配结果已保存为 PNG 和 NPZ")
elif len(mkpts0) > 0:
    print("没有内点，仅显示所有匹配点")
    vis_img = plot_matches(image0, image1, mkpts0, mkpts1, color=(255, 0, 0))
    plt.figure(figsize=(20, 10))
    plt.imshow(vis_img)
    plt.axis('off')
    plt.title(f'SFD2+IMP 匹配结果：{len(mkpts0)} 对匹配点（无内点）')
    plt.savefig('匹配结果_所有点.png', bbox_inches='tight')
    plt.show()
else:
    print("没有有效匹配点，无法生成匹配图")
