import argparse  # 用于解析命令行参数
import logging   # 用于打印日志信息
import time
from pathlib import Path  # 用于处理路径对象（跨平台）

import cv2  # OpenCV库，图像处理核心工具
import numpy as np

from src.images import Image  # 自定义的图像类，封装了读取、缩放、提取特征
from src.matching.imp_matcher import IMPMatcher  # 特征匹配器（基于你使用的IMP算法）
from imcui.hloc.extractors.sfd2 import SFD2  # SFD2特征提取器
from src.config import get_config, print_config  # 配置管理
from src.matching import find_connected_components, build_homographies  # 图像连接图构建和单应矩阵计算
from src.rendering import set_gain_compensations, multi_band_blending, simple_blending  # 增益补偿和图像融合方法
def parse_args():
    parser = argparse.ArgumentParser(description="Panorama stitching using SFD2+IMP.")

    # 指定图像所在文件夹
    parser.add_argument("data_dir", type=Path, help="Directory containing images")

    # 图像最大边长（超过该值则缩放）
    parser.add_argument("--size", type=int, default=None, help="Resize images to max dimension")

    # 是否使用多频带融合（默认关闭）
    parser.add_argument("--multi-band", action="store_true", help="Use multi-band blending")

    # 多频带融合中的频带数量
    parser.add_argument("--num-bands", type=int, default=5, help="Number of bands for multi-band blending")

    # 多频带融合中的高斯模糊参数
    parser.add_argument("--mbb-sigma", type=float, default=1.0, help="Sigma for multi-band blending")

    # 增益补偿参数 sigma_n（用于控制噪声）
    parser.add_argument("--gain-sigma-n", type=float, default=10, help="Gain compensation sigma_n")

    # 增益补偿参数 sigma_g（用于控制梯度平滑）
    parser.add_argument("--gain-sigma-g", type=float, default=0.1, help="Gain compensation sigma_g")

    # 是否开启详细日志
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose logging")

    # 是否使用顺序匹配（适用于按顺序拍摄的图像）
    parser.add_argument("--sequential", action="store_true", help="Use sequential matching for images captured in order (faster)")

    # 顺序匹配的邻接范围
    parser.add_argument("--seq-range", type=int, default=1, help="Range for sequential matching (1=adjacent only, 2=adjacent+skip-one, etc.)")

    # 快速模式
    parser.add_argument("--fast", action="store_true", help="Fast mode: reduce feature points and increase matching threshold for speed")

    # 宽松模式
    parser.add_argument("--loose", action="store_true", help="Loose mode: lower matching threshold for difficult sequences")

    # 超宽松模式
    parser.add_argument("--ultra-loose", action="store_true", help="Ultra loose mode: maximum features and minimum threshold for very difficult sequences")

    return parser.parse_args()
def main():
    args = parse_args()  # 解析命令行参数
    if args.verbose:
        logging.basicConfig(level=logging.INFO)  # 设置日志等级为 INFO

    # 支持的图像扩展名
    valid_exts = {".jpg", ".png", ".bmp", ".jpeg"}
    image_paths = sorted([
        str(p) for p in args.data_dir.iterdir()
        if p.suffix.lower() in valid_exts  # 过滤图像文件
    ])

    # 图像数量不足时退出
    if len(image_paths) < 2:
        logging.error("Need at least two images to stitch.")
        return
    # 第一步：加载图像并提取 SFD2 特征
    logging.info("Loading and extracting features...")
    images = [Image(path, args.size) for path in image_paths]  # 创建 Image 对象
    for img in images:
        img.compute_features()  # 提取 keypoints 和 descriptors
    # 第二步：进行图像对之间的特征匹配
    logging.info("Matching features between image pairs...")

    # 根据模式选择配置
    if args.fast:
        config_mode = "fast"
        print("Fast mode enabled")
    elif args.ultra_loose:
        config_mode = "ultra_loose"
        print("Ultra loose mode enabled")
    elif args.loose:
        config_mode = "loose"
        print("Loose mode enabled")
    else:
        config_mode = "default"
        print("Default mode")

    # 获取配置
    config = get_config(config_mode)
    print_config(config)

    # 应用SFD2配置
    Image.sfd2_extractor = SFD2(config['sfd2'])

    # 创建IMP匹配器
    matcher = IMPMatcher(
        match_threshold=config['imp']['match_threshold'],
        max_keypoints=config['imp']['max_keypoints']
    )

    # 构建所有图像的匹配图（内部会调用 matcher.match）
    from src.matching import PairMatch, MultiImageMatches
    match_graph = MultiImageMatches(images, matcher, sequential_matching=args.sequential, seq_range=args.seq_range)
    pair_matches: list[PairMatch] = match_graph.get_pair_matches()

    # 按匹配点数排序（匹配质量高的排前）
    pair_matches.sort(key=lambda pm: len(pm.matches), reverse=True)
    # 第三步：构建连通图（找出拼接组）
    logging.info("Finding connected components...")
    connected_components = find_connected_components(pair_matches)
    logging.info(f"Found {len(connected_components)} connected components")
    # 第四步：为每组图像构建单应性矩阵
    logging.info("Estimating homographies...")
    build_homographies(connected_components, pair_matches)
    # 第五步：亮度增益补偿（避免图像亮度突变）
    logging.info("Computing gain compensation...")
    for component in connected_components:
        comp_matches = [pm for pm in pair_matches if pm.image_a in component]  # 找到当前组的匹配对
        set_gain_compensations(
            component, comp_matches,
            sigma_n=args.gain_sigma_n,
            sigma_g=args.gain_sigma_g
        )
    # 第六步：将增益值应用到图像上（逐像素乘 gain）
    for img in images:
        img.image = np.clip((img.image * img.gain[np.newaxis, np.newaxis, :]), 0, 255).astype(np.uint8)
    # 第七步：图像融合拼接（支持 multi-band 或 simple）
    logging.info("Stitching images...")
    if args.multi_band:
        results = [
            multi_band_blending(component, args.num_bands, args.mbb_sigma)
            for component in connected_components
        ]
    else:
        results = [
            simple_blending(component)
            for component in connected_components
        ]
    # 第八步：保存拼接图像结果
    output_dir = args.data_dir / "results"
    output_dir.mkdir(exist_ok=True)
    for i, result in enumerate(results):
        cv2.imwrite(str(output_dir / f"pano_{i}.jpg"), result)  # pano_0.jpg, pano_1.jpg ...

    logging.info(f"Saved {len(results)} panoramas to {output_dir}")
if __name__ == "__main__":
    main()
