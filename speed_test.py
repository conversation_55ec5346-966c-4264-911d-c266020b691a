#!/usr/bin/env python3
"""
速度优化测试脚本：测试不同参数配置的性能
"""

import argparse
import time
import subprocess
import sys
from pathlib import Path

def run_test(data_dir, config_name, cmd_args, max_images=20):
    """运行单个测试配置"""
    print(f"\n{'='*60}")
    print(f"🧪 测试配置: {config_name}")
    print(f"📝 参数: {' '.join(cmd_args)}")
    print(f"{'='*60}")
    
    # 限制图像数量以加快测试
    test_dir = Path("speed_test_subset")
    if test_dir.exists():
        import shutil
        shutil.rmtree(test_dir)
    
    test_dir.mkdir()
    
    # 复制前N张图像
    valid_exts = {".jpg", ".png", ".bmp", ".jpeg"}
    image_paths = sorted([
        p for p in data_dir.iterdir()
        if p.suffix.lower() in valid_exts
    ])[:max_images]
    
    print(f"📸 使用 {len(image_paths)} 张图像进行测试")
    
    # 复制图像到测试目录
    for i, src_path in enumerate(image_paths):
        dst_path = test_dir / src_path.name
        import shutil
        shutil.copy2(src_path, dst_path)
    
    # 构建完整命令
    cmd = ["python", "nnewmain.py", str(test_dir)] + cmd_args + ["-v"]
    
    # 运行测试
    start_time = time.time()
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        end_time = time.time()
        
        if result.returncode == 0:
            total_time = end_time - start_time
            print(f"✅ 测试成功完成")
            print(f"⏱️  总耗时: {total_time:.1f}秒")
            
            # 解析日志中的详细时间
            output_lines = result.stdout.split('\n')
            feature_time = None
            match_time = None
            
            for line in output_lines:
                if "特征提取耗时" in line:
                    try:
                        feature_time = float(line.split(':')[1].strip().replace('s', ''))
                    except:
                        pass
                elif "特征匹配耗时" in line:
                    try:
                        match_time = float(line.split(':')[1].strip().replace('s', ''))
                    except:
                        pass
            
            if feature_time:
                print(f"   特征提取: {feature_time:.1f}s")
            if match_time:
                print(f"   特征匹配: {match_time:.1f}s")
            
            return total_time, feature_time, match_time
        else:
            print(f"❌ 测试失败")
            print(f"错误输出: {result.stderr}")
            return None, None, None
            
    except subprocess.TimeoutExpired:
        print(f"⏰ 测试超时 (>10分钟)")
        return None, None, None
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return None, None, None
    finally:
        # 清理测试目录
        if test_dir.exists():
            import shutil
            shutil.rmtree(test_dir)

def main():
    parser = argparse.ArgumentParser(description="速度优化测试")
    parser.add_argument("data_dir", type=Path, help="图像目录")
    parser.add_argument("--max-images", type=int, default=20, help="测试图像数量")
    
    args = parser.parse_args()
    
    if not args.data_dir.exists():
        print(f"错误: 目录 {args.data_dir} 不存在")
        return
    
    print("🚀 图像拼接速度优化测试")
    print(f"📁 测试目录: {args.data_dir}")
    print(f"📸 测试图像数量: {args.max_images}")
    
    # 测试配置列表
    test_configs = [
        {
            "name": "默认配置",
            "args": ["--sequential", "--size", "1000"]
        },
        {
            "name": "快速模式",
            "args": ["--sequential", "--fast", "--size", "1000"]
        },
        {
            "name": "小尺寸图像",
            "args": ["--sequential", "--size", "800"]
        },
        {
            "name": "快速模式+小尺寸",
            "args": ["--sequential", "--fast", "--size", "800"]
        },
        {
            "name": "超快模式",
            "args": ["--sequential", "--fast", "--size", "600"]
        }
    ]
    
    results = []
    
    # 运行所有测试
    for config in test_configs:
        total_time, feature_time, match_time = run_test(
            args.data_dir, 
            config["name"], 
            config["args"], 
            args.max_images
        )
        
        results.append({
            "name": config["name"],
            "args": config["args"],
            "total_time": total_time,
            "feature_time": feature_time,
            "match_time": match_time
        })
    
    # 输出对比结果
    print(f"\n{'='*80}")
    print("📊 性能对比结果")
    print(f"{'='*80}")
    
    print(f"{'配置':<15} {'总时间':<10} {'特征提取':<10} {'特征匹配':<10} {'相对速度':<10}")
    print("-" * 80)
    
    baseline_time = None
    for result in results:
        if result["total_time"] is not None:
            if baseline_time is None:
                baseline_time = result["total_time"]
                speedup = "1.0x"
            else:
                speedup = f"{baseline_time / result['total_time']:.1f}x"
            
            print(f"{result['name']:<15} "
                  f"{result['total_time']:<10.1f} "
                  f"{result['feature_time'] or 0:<10.1f} "
                  f"{result['match_time'] or 0:<10.1f} "
                  f"{speedup:<10}")
        else:
            print(f"{result['name']:<15} {'失败':<10} {'--':<10} {'--':<10} {'--':<10}")
    
    # 推荐配置
    print(f"\n{'='*80}")
    print("💡 推荐配置")
    print(f"{'='*80}")
    
    fastest_config = min([r for r in results if r["total_time"] is not None], 
                        key=lambda x: x["total_time"], default=None)
    
    if fastest_config:
        print(f"🏆 最快配置: {fastest_config['name']}")
        print(f"⚡ 推荐命令: python nnewmain.py your_data_dir {' '.join(fastest_config['args'])}")
        
        if baseline_time and fastest_config["total_time"]:
            speedup = baseline_time / fastest_config["total_time"]
            print(f"🚀 速度提升: {speedup:.1f}x")
    
    print(f"\n💡 进一步优化建议:")
    print(f"1. 使用GPU版本可获得4-6x额外加速")
    print(f"2. 减少图像分辨率 (--size 参数)")
    print(f"3. 使用更高的匹配阈值 (--fast 模式)")
    print(f"4. 考虑分批处理大量图像")

if __name__ == "__main__":
    main()
