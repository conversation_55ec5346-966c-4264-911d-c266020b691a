[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "imcui"
description = "Image Matching Webui: A tool for matching images using sota algorithms with a Gradio UI"
version = "0.0.3"
authors = [
    {name = "vincentqyw"},
]
readme = "README.md"
requires-python = ">=3.9"
license = {file = "LICENSE"}
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
]
urls = {Repository = "https://github.com/Vincentqyw/image-matching-webui"}
dynamic = ["dependencies"]


[project.optional-dependencies]
dev = ["black", "flake8", "isort"]


[tool.setuptools]
packages = { find = { include = ["imcui*"] } }
include-package-data = true


[tool.setuptools.dynamic]
dependencies = {file = ["requirements.txt"]}


[tool.pytest.ini_options]
minversion = "6.0"
addopts = ["-ra", "--showlocals", "--strict-markers", "--strict-config"]
xfail_strict = true
testpaths = ["tests"]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::UserWarning",
    "ignore::FutureWarning",
    "ignore::RuntimeWarning",
]
