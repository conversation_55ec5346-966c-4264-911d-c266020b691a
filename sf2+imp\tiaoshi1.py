
from imcui.hloc.extractors.sfd2 import SFD2
from imcui.hloc.matchers.imp import IMP
import torch
import numpy as np
import cv2
import matplotlib.pyplot as plt
from PIL import Image
import os

# 加载 SFD2 特征提取器
sfd2_extractor = SFD2({'max_keypoints': 4096, 'conf_th': 0.001})

# 加载 IMP 特征匹配器
imp_matcher = IMP({'match_threshold': 0.2, 'features': 'sfd2'})

# 使用 PIL 加载中文路径的灰度图，并进行缩放
def load_gray_image_unicode(path, resize_scale=0.5):
    img = Image.open(path).convert('L')
    if resize_scale != 1.0:
        new_size = (int(img.width * resize_scale), int(img.height * resize_scale))
        img = img.resize(new_size, Image.BILINEAR)
    return np.array(img)

# 图像路径（支持中文）
img_path0 = 'G:/大满站数据/0704/scan_0704_221357/yangdai/photo_2D_80ns_A/1.bmp'
img_path1 = 'G:/大满站数据/0704/scan_0704_221357/yangdai/photo_2D_80ns_A/2.bmp'

# 加载灰度图并降采样
image0 = load_gray_image_unicode(img_path0, resize_scale=0.5)
image1 = load_gray_image_unicode(img_path1, resize_scale=0.5)

if image0 is None or image1 is None:
    raise FileNotFoundError("无法读取图像，请检查路径是否正确")

# 转为 [1, 3, H, W]，供 SFD2 使用
def preprocess_gray(image):
    image = np.stack([image]*3, axis=-1)  # 灰度转3通道
    return torch.from_numpy(image).float().permute(2, 0, 1).unsqueeze(0) / 255.0

image0_tensor = preprocess_gray(image0)
image1_tensor = preprocess_gray(image1)

# 特征提取
pred0 = sfd2_extractor({'image': image0_tensor})
pred1 = sfd2_extractor({'image': image1_tensor})

# 特征匹配
matches = imp_matcher({
    'image0': image0_tensor,
    'keypoints0': pred0['keypoints'],
    'scores0': pred0['scores'],
    'descriptors0': pred0['descriptors'],
    'image1': image1_tensor,
    'keypoints1': pred1['keypoints'],
    'scores1': pred1['scores'],
    'descriptors1': pred1['descriptors'],
})

# 解析匹配结果
keypoints0 = pred0['keypoints'][0].cpu().numpy()
keypoints1 = pred1['keypoints'][0].cpu().numpy()

if 'matches0' in matches:
    matches0 = matches['matches0'][0].cpu().numpy()
    valid = matches0 > -1
    mkpts0 = keypoints0[valid]
    mkpts1 = keypoints1[matches0[valid]]
elif 'keypoints0' in matches and 'keypoints1' in matches:
    mkpts0 = matches['keypoints0'].cpu().numpy()
    mkpts1 = matches['keypoints1'].cpu().numpy()
else:
    print("未知匹配结果格式，返回键包含：", matches.keys())
    mkpts0 = np.array([])
    mkpts1 = np.array([])

print(f"共找到 {len(mkpts0)} 对匹配点")

# 计算单应性矩阵
if len(mkpts0) >= 4:
    H, mask = cv2.findHomography(mkpts0, mkpts1, cv2.RANSAC, 3.0)
    inliers = mask.ravel().astype(bool)
    inlier_mkpts0 = mkpts0[inliers]
    inlier_mkpts1 = mkpts1[inliers]
    print(f"内点数量: {np.sum(inliers)}")
else:
    print("匹配点不足，无法计算单应性矩阵")
    H = None
    inlier_mkpts0 = np.array([])
    inlier_mkpts1 = np.array([])

# 匹配可视化函数
def plot_matches(img1, img2, pts1, pts2, color=(0, 255, 0)):
    h1, w1 = img1.shape[:2]
    h2, w2 = img2.shape[:2]
    vis = np.zeros((max(h1, h2), w1 + w2, 3), dtype=np.uint8)
    vis[:h1, :w1] = cv2.cvtColor(img1, cv2.COLOR_GRAY2BGR)
    vis[:h2, w1:w1 + w2] = cv2.cvtColor(img2, cv2.COLOR_GRAY2BGR)
    for pt1, pt2 in zip(pts1, pts2):
        pt1 = tuple(map(int, pt1))
        pt2 = (int(pt2[0] + w1), int(pt2[1]))
        cv2.circle(vis, pt1, 3, color, -1)
        cv2.circle(vis, pt2, 3, color, -1)
        cv2.line(vis, pt1, pt2, color, 1)
    return vis

# 可视化结果并保存
if len(inlier_mkpts0) > 0:
    vis_img = plot_matches(image0, image1, inlier_mkpts0, inlier_mkpts1)
    plt.figure(figsize=(20, 10))
    plt.imshow(vis_img)
    plt.axis('off')
    plt.title(f'SFD2+IMP 匹配结果：{len(inlier_mkpts0)} 对内点（总匹配 {len(mkpts0)}）')
    plt.savefig('匹配结果_内点.png', bbox_inches='tight')
    plt.show()
    np.savez('匹配结果_内点.npz', 
             keypoints0=inlier_mkpts0, 
             keypoints1=inlier_mkpts1, 
             homography=H)
    print("匹配结果已保存为 PNG 和 NPZ")
elif len(mkpts0) > 0:
    print("没有内点，仅显示所有匹配点")
    vis_img = plot_matches(image0, image1, mkpts0, mkpts1, color=(255, 0, 0))
    plt.figure(figsize=(20, 10))
    plt.imshow(vis_img)
    plt.axis('off')
    plt.title(f'SFD2+IMP 匹配结果：{len(mkpts0)} 对匹配点（无内点）')
    plt.savefig('匹配结果_所有点.png', bbox_inches='tight')
    plt.show()
else:
    print("没有有效匹配点，无法生成匹配图")
