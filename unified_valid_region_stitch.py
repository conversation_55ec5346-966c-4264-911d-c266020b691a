import cv2
import numpy as np

def create_unified_mask(img1, img2, img3, img4):
    """
    创建统一的有效像素掩码
    取所有图像有效区域的交集，确保所有图像使用相同的有效区域
    """
    
    # 计算每个图像的有效区域
    if len(img1.shape) == 3:
        mask1 = (img1.sum(axis=2) > 0)
        mask2 = (img2.sum(axis=2) > 0)
        mask3 = (img3.sum(axis=2) > 0)
        mask4 = (img4.sum(axis=2) > 0)
    else:
        mask1 = (img1 > 0)
        mask2 = (img2 > 0)
        mask3 = (img3 > 0)
        mask4 = (img4 > 0)
    
    # 计算所有图像的有效区域交集
    unified_mask = mask1 & mask2 & mask3 & mask4
    
    print(f"原始有效像素数:")
    print(f"  图像1: {np.sum(mask1)}")
    print(f"  图像2: {np.sum(mask2)}")
    print(f"  图像3: {np.sum(mask3)}")
    print(f"  图像4: {np.sum(mask4)}")
    print(f"统一有效像素数: {np.sum(unified_mask)}")
    
    return unified_mask

def apply_unified_mask(image, mask):
    """将统一掩码应用到图像上"""
    result = image.copy()
    if len(result.shape) == 3:
        result[~mask] = [0, 0, 0]  # 无效区域设为黑色
    else:
        result[~mask] = 0
    return result

def stitch_with_unified_region(img1_path, img2_path, H, output_name, unified_mask):
    """
    使用统一有效区域进行拼接
    """
    
    print(f"\n{'='*60}")
    print(f"拼接: {img1_path} + {img2_path}")
    print(f"{'='*60}")
    
    # 读取图像
    img1 = cv2.imread(img1_path)
    img2 = cv2.imread(img2_path)
    
    # 应用统一掩码
    img1_masked = apply_unified_mask(img1, unified_mask)
    img2_masked = apply_unified_mask(img2, unified_mask)
    
    print(f"应用统一掩码后:")
    print(f"  图像1有效像素: {np.sum(img1_masked.sum(axis=2) > 0)}")
    print(f"  图像2有效像素: {np.sum(img2_masked.sum(axis=2) > 0)}")
    
    # 计算变换后的图像尺寸
    h1, w1 = img1_masked.shape[:2]
    h2, w2 = img2_masked.shape[:2]
    
    # 计算图像2的四个角点变换后的位置
    corners = np.float32([[0, 0], [w2, 0], [w2, h2], [0, h2]]).reshape(-1, 1, 2)
    transformed_corners = cv2.perspectiveTransform(corners, H)
    
    # 计算新画布的尺寸
    all_corners = np.vstack([
        [[0, 0], [w1, 0], [w1, h1], [0, h1]],
        transformed_corners.reshape(-1, 2)
    ])
    
    x_min, y_min = np.min(all_corners, axis=0)
    x_max, y_max = np.max(all_corners, axis=0)
    
    new_width = int(x_max - x_min)
    new_height = int(y_max - y_min)
    
    print(f"新画布尺寸: {new_width}x{new_height}")
    
    # 创建平移矩阵
    translation = np.array([
        [1, 0, -x_min],
        [0, 1, -y_min],
        [0, 0, 1]
    ])
    
    # 变换图像
    result_img1 = cv2.warpPerspective(img1_masked, translation, (new_width, new_height))
    combined_H = np.dot(translation, H)
    result_img2 = cv2.warpPerspective(img2_masked, combined_H, (new_width, new_height))
    
    # 灰度值加权平均融合
    mask1 = (result_img1.sum(axis=2) > 0)
    mask2 = (result_img2.sum(axis=2) > 0)
    overlap = mask1 & mask2
    
    final_result = np.zeros_like(result_img1)
    final_result[mask1 & ~overlap] = result_img1[mask1 & ~overlap]
    final_result[mask2 & ~overlap] = result_img2[mask2 & ~overlap]
    
    if np.any(overlap):
        img1_overlap = result_img1[overlap].astype(np.float32)
        img2_overlap = result_img2[overlap].astype(np.float32)
        
        # 计算灰度值
        gray1 = 0.114 * img1_overlap[:, 0] + 0.587 * img1_overlap[:, 1] + 0.299 * img1_overlap[:, 2]
        gray2 = 0.114 * img2_overlap[:, 0] + 0.587 * img2_overlap[:, 1] + 0.299 * img2_overlap[:, 2]
        
        # 计算权重
        epsilon = 1e-6
        weight1 = gray1 + epsilon
        weight2 = gray2 + epsilon
        
        total_weight = weight1 + weight2
        weight1_norm = weight1 / total_weight
        weight2_norm = weight2 / total_weight
        
        # 加权融合
        blended = np.zeros_like(img1_overlap)
        for c in range(3):
            blended[:, c] = (img1_overlap[:, c] * weight1_norm + 
                            img2_overlap[:, c] * weight2_norm)
        
        final_result[overlap] = blended.astype(np.uint8)
        
        print(f"重叠区域像素数: {np.sum(overlap)}")
        print(f"图像1平均灰度: {np.mean(gray1):.1f}")
        print(f"图像2平均灰度: {np.mean(gray2):.1f}")
        print(f"图像1平均权重: {np.mean(weight1_norm):.3f}")
        print(f"图像2平均权重: {np.mean(weight2_norm):.3f}")
    else:
        print("没有重叠区域")
    
    # 保存结果
    cv2.imwrite(output_name, final_result)
    print(f"结果保存为: {output_name}")
    
    return np.sum(overlap) if np.any(overlap) else 0

def main():
    # 读取所有图像
    img1 = cv2.imread("D:/daima/gitup/7month/image-stitching-main/testdata3/1.bmp")
    img2 = cv2.imread("D:/daima/gitup/7month/image-stitching-main/testdata3/2.bmp")
    img3 = cv2.imread("D:/daima/gitup/7month/image-stitching-main/testdata3/3.bmp")
    img4 = cv2.imread("D:/daima/gitup/7month/image-stitching-main/testdata3/4.bmp")
    
    print("🔍 统一有效像素区域拼接测试")
    print("="*60)
    
    # 创建统一掩码
    unified_mask = create_unified_mask(img1, img2, img3, img4)
    
    # 使用2.bmp和4.bmp的H矩阵
    H = np.array([
        [1.118417764482483, 0.0603628696196226, 948.2571340071909],
        [-0.0603628696196226, 1.118417764482483, -66.74286599280914],
        [0.0, 0.0, 1.0]
    ])
    
    # 进行拼接测试
    overlap1 = stitch_with_unified_region(
        "D:/daima/gitup/7month/image-stitching-main/testdata3/2.bmp",
        "D:/daima/gitup/7month/image-stitching-main/testdata3/4.bmp",
        H,
        "unified_2_4.bmp",
        unified_mask
    )
    
    overlap2 = stitch_with_unified_region(
        "D:/daima/gitup/7month/image-stitching-main/testdata3/1.bmp",
        "D:/daima/gitup/7month/image-stitching-main/testdata3/3.bmp",
        H,
        "unified_1_3.bmp",
        unified_mask
    )
    
    print(f"\n{'='*60}")
    print("统一有效区域后的结果对比:")
    print(f"{'='*60}")
    print(f"2.bmp + 4.bmp 重叠像素数: {overlap1}")
    print(f"1.bmp + 3.bmp 重叠像素数: {overlap2}")
    print(f"重叠像素数差异: {abs(overlap1 - overlap2)}")
    
    if abs(overlap1 - overlap2) == 0:
        print("✅ 成功！使用统一有效区域后，重叠像素数完全相同")
    elif abs(overlap1 - overlap2) < 100:
        print("🔶 基本成功！重叠像素数差异很小（可能由于插值误差）")
    else:
        print("⚠️  仍有差异，需要进一步分析")

if __name__ == "__main__":
    main()
