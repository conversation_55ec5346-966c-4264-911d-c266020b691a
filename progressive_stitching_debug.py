#!/usr/bin/env python3
"""
渐进式拼接调试工具
从少量图像开始，逐步增加，找出问题出现的位置
"""

import cv2
import numpy as np
import argparse
from pathlib import Path
import json

from src.images import Image
from src.matching.imp_matcher import IMPMatcher
from src.matching.pair_match import PairMatch
from src.matching.multi_images_matches import MultiImageMatches
from src.config import get_config
from src.matching.multi_images_matches import natural_sort_key

def simple_stitch_images(images, pair_matches):
    """简单的图像拼接函数"""
    if len(images) < 2:
        return images[0].image if images else None

    # 从第一张图像开始
    result = images[0].image.copy()

    for i in range(1, len(images)):
        # 找到当前图像的匹配
        current_match = None
        for pm in pair_matches:
            if ((pm.image_a == images[i-1] and pm.image_b == images[i]) or
                (pm.image_a == images[i] and pm.image_b == images[i-1])):
                current_match = pm
                break

        if current_match is None:
            print(f"    警告: 没有找到图像 {i-1} 和 {i} 的匹配")
            continue

        # 获取单应性矩阵
        if current_match.H is None:
            current_match.compute_homography()

        H = current_match.H
        if H is None:
            print(f"    警告: 图像 {i-1} 和 {i} 的单应性矩阵为空")
            continue

        # 计算变换后的图像尺寸
        h1, w1 = result.shape[:2]
        h2, w2 = images[i].image.shape[:2]

        # 变换第二张图像的四个角点
        corners = np.float32([[0, 0], [w2, 0], [w2, h2], [0, h2]]).reshape(-1, 1, 2)
        transformed_corners = cv2.perspectiveTransform(corners, H)

        # 计算新画布的边界
        all_corners = np.vstack([
            [[0, 0], [w1, 0], [w1, h1], [0, h1]],
            transformed_corners.reshape(-1, 2)
        ])

        x_min, y_min = np.min(all_corners, axis=0)
        x_max, y_max = np.max(all_corners, axis=0)

        # 检查尺寸是否合理
        new_width = int(x_max - x_min)
        new_height = int(y_max - y_min)

        if new_width > 20000 or new_height > 20000:
            print(f"    错误: 画布尺寸过大 {new_width}x{new_height}")
            return None

        # 创建平移矩阵
        translation = np.array([[1, 0, -x_min], [0, 1, -y_min], [0, 0, 1]])

        # 变换现有结果
        result = cv2.warpPerspective(result, translation, (new_width, new_height))

        # 变换并添加新图像
        final_H = translation @ H
        warped_new = cv2.warpPerspective(images[i].image, final_H, (new_width, new_height))

        # 简单叠加
        mask = (warped_new.sum(axis=2) > 0)
        result[mask] = warped_new[mask]

        print(f"    步骤 {i}: 添加图像 {Path(images[i].path).stem}, 画布: {new_width}x{new_height}")

    return result

def test_subset_stitching(images, start_idx, count, config_mode, output_dir):
    """测试指定数量图像的拼接"""
    
    subset_images = images[start_idx:start_idx + count]
    subset_name = f"images_{start_idx+1}_to_{start_idx+count}"
    
    print(f"\n{'='*60}")
    print(f"🔍 测试 {count} 张图像: {subset_name}")
    print(f"图像范围: {Path(subset_images[0].path).stem} → {Path(subset_images[-1].path).stem}")
    print(f"{'='*60}")
    
    # 创建输出目录
    subset_dir = output_dir / subset_name
    subset_dir.mkdir(exist_ok=True)
    
    try:
        # 获取配置
        config = get_config(config_mode)
        Image.sfd2_extractor = Image.sfd2_extractor.__class__(config['sfd2'])
        
        # 创建匹配器
        matcher = IMPMatcher(
            match_threshold=config['imp']['match_threshold'],
            max_keypoints=config['imp']['max_keypoints']
        )
        
        # 1. 分析匹配情况
        print("🎯 分析图像匹配...")
        match_results = []
        
        for i in range(len(subset_images) - 1):
            img_a = subset_images[i]
            img_b = subset_images[i + 1]
            
            # 提取特征
            img_a.compute_features()
            img_b.compute_features()
            
            # 匹配
            mkpts0, mkpts1 = matcher.match(img_a, img_b)
            
            if len(mkpts0) >= 4:
                # 计算单应性矩阵
                H, status = cv2.findHomography(mkpts1, mkpts0, cv2.RANSAC, 5.0, maxIters=500)
                
                if H is not None:
                    inliers = np.sum(status)
                    inlier_ratio = inliers / len(mkpts0)
                    
                    match_results.append({
                        'pair': f"{Path(img_a.path).stem}→{Path(img_b.path).stem}",
                        'matches': len(mkpts0),
                        'inliers': int(inliers),
                        'inlier_ratio': float(inlier_ratio),
                        'homography': H,
                        'success': True
                    })
                    
                    print(f"  ✅ {Path(img_a.path).stem}→{Path(img_b.path).stem}: "
                          f"{inliers}/{len(mkpts0)} 内点 ({inlier_ratio:.1%})")
                else:
                    match_results.append({
                        'pair': f"{Path(img_a.path).stem}→{Path(img_b.path).stem}",
                        'matches': len(mkpts0),
                        'success': False,
                        'error': 'Homography failed'
                    })
                    print(f"  ❌ {Path(img_a.path).stem}→{Path(img_b.path).stem}: 单应性矩阵计算失败")
            else:
                match_results.append({
                    'pair': f"{Path(img_a.path).stem}→{Path(img_b.path).stem}",
                    'matches': len(mkpts0),
                    'success': False,
                    'error': 'Insufficient matches'
                })
                print(f"  ❌ {Path(img_a.path).stem}→{Path(img_b.path).stem}: 匹配点不足 ({len(mkpts0)})")
        
        # 2. 尝试拼接
        print("\n🔧 尝试拼接...")
        
        # 使用原始拼接算法
        match_graph = MultiImageMatches(subset_images, matcher, sequential_matching=True)
        pair_matches = match_graph.get_pair_matches()
        
        print(f"  有效匹配对数: {len(pair_matches)}")
        
        if len(pair_matches) >= len(subset_images) - 1:
            # 使用简单的拼接方法
            try:
                panorama = simple_stitch_images(subset_images, pair_matches)

                if panorama is not None:
                    # 保存结果
                    result_path = subset_dir / "panorama.jpg"
                    cv2.imwrite(str(result_path), panorama)
                    
                    print(f"  ✅ 拼接成功! 尺寸: {panorama.shape[1]}x{panorama.shape[0]}")
                    print(f"  📁 保存到: {result_path}")
                    
                    # 保存详细信息
                    info = {
                        'image_count': count,
                        'image_range': f"{Path(subset_images[0].path).stem} → {Path(subset_images[-1].path).stem}",
                        'panorama_size': [panorama.shape[1], panorama.shape[0]],
                        'match_results': match_results,
                        'stitching_success': True,
                        'config_mode': config_mode
                    }
                    
                    with open(subset_dir / "info.json", 'w', encoding='utf-8') as f:
                        json.dump(info, f, indent=2, ensure_ascii=False, default=str)
                    
                    return True, panorama.shape, None
                else:
                    print(f"  ❌ 拼接失败: 返回空结果")
                    return False, None, "Empty result"
                    
            except Exception as e:
                print(f"  ❌ 拼接异常: {e}")
                return False, None, str(e)
        else:
            print(f"  ❌ 匹配对不足: {len(pair_matches)}/{len(subset_images)-1}")
            return False, None, "Insufficient matches"
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False, None, str(e)

def progressive_debug(data_dir, output_dir="progressive_debug", config_mode="default", size=1000, 
                     start_count=2, max_count=None, step=1):
    """渐进式调试主函数"""
    
    print("🚀 开始渐进式拼接调试...")
    
    # 获取图像列表
    valid_exts = {".jpg", ".png", ".bmp", ".jpeg"}
    image_paths = sorted([
        p for p in data_dir.iterdir()
        if p.suffix.lower() in valid_exts
    ], key=lambda p: natural_sort_key(str(p)))
    
    if len(image_paths) < 2:
        print("❌ 需要至少两张图像")
        return
    
    print(f"📸 找到 {len(image_paths)} 张图像")
    print(f"🔧 使用配置: {config_mode}")
    print(f"📁 输出目录: {output_dir}")
    
    # 加载图像
    images = [Image(str(path), size) for path in image_paths]
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # 渐进式测试
    max_count = max_count or len(images)
    results = []
    
    print(f"\n🎯 开始渐进式测试: {start_count} → {max_count} 张图像")
    
    for count in range(start_count, min(max_count + 1, len(images) + 1), step):
        # 测试从第一张图像开始的子集
        success, size_info, error = test_subset_stitching(
            images, 0, count, config_mode, output_path
        )
        
        result = {
            'count': count,
            'success': success,
            'size': size_info,
            'error': error
        }
        results.append(result)
        
        if not success:
            print(f"\n⚠️  在 {count} 张图像时首次失败!")
            print(f"   错误: {error}")
            
            # 如果失败，尝试不同的起始位置
            if count > 3:
                print(f"\n🔄 尝试不同的起始位置...")
                for start_idx in range(1, min(4, len(images) - count + 1)):
                    print(f"\n   尝试从第 {start_idx + 1} 张图像开始...")
                    alt_success, alt_size, alt_error = test_subset_stitching(
                        images, start_idx, count, config_mode, output_path
                    )
                    
                    if alt_success:
                        print(f"   ✅ 从第 {start_idx + 1} 张图像开始成功!")
                        break
                    else:
                        print(f"   ❌ 从第 {start_idx + 1} 张图像开始也失败: {alt_error}")
            
            break
    
    # 生成总结报告
    print(f"\n📊 渐进式调试总结:")
    print(f"{'图像数':<8} {'状态':<8} {'全景图尺寸':<15} {'错误信息'}")
    print("-" * 50)
    
    for result in results:
        status = "✅ 成功" if result['success'] else "❌ 失败"
        size_str = f"{result['size'][0]}x{result['size'][1]}" if result['size'] else "N/A"
        error_str = result['error'] if result['error'] else ""
        
        print(f"{result['count']:<8} {status:<8} {size_str:<15} {error_str}")
    
    # 保存总结报告
    summary = {
        'total_images': len(images),
        'config_mode': config_mode,
        'results': results,
        'first_failure': next((r for r in results if not r['success']), None)
    }
    
    with open(output_path / "summary.json", 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n📁 完整报告保存在: {output_path.absolute()}")

def main():
    parser = argparse.ArgumentParser(description="渐进式拼接调试")
    parser.add_argument("data_dir", type=Path, help="图像目录")
    parser.add_argument("--output", type=str, default="progressive_debug", help="输出目录")
    parser.add_argument("--config", type=str, default="default", help="配置模式")
    parser.add_argument("--size", type=int, default=1000, help="图像缩放尺寸")
    parser.add_argument("--start", type=int, default=2, help="起始图像数量")
    parser.add_argument("--max", type=int, default=None, help="最大图像数量")
    parser.add_argument("--step", type=int, default=1, help="每次增加的图像数量")
    
    args = parser.parse_args()
    
    progressive_debug(
        args.data_dir, args.output, args.config, args.size,
        args.start, args.max, args.step
    )

if __name__ == "__main__":
    main()
