import torch
import numpy as np
import cv2
from imcui.hloc.matchers.imp import IMP
from typing import Tuple


class IMPMatcherGPU:
    """GPU优化的IMP特征匹配器"""
    
    def __init__(self, match_threshold=0.1, device=None, mixed_precision=False):
        self.device = device if device is not None else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.mixed_precision = mixed_precision
        
        # 初始化IMP匹配器
        self.matcher = IMP({'match_threshold': match_threshold, 'features': 'sfd2'})
        
        # 将匹配器移动到GPU
        if hasattr(self.matcher, 'net') and self.device.type == 'cuda':
            self.matcher.net = self.matcher.net.to(self.device)
            
        print(f"🔧 IMP匹配器初始化完成 (设备: {self.device}, 阈值: {match_threshold})")

    def match(self, image_a, image_b) -> Tuple[np.ndarray, np.ndarray]:
        """
        匹配两张图像的特征点
        
        Args:
            image_a: 第一张图像 (ImageGPU对象)
            image_b: 第二张图像 (ImageGPU对象)
            
        Returns:
            mkpts0: 第一张图像的匹配点
            mkpts1: 第二张图像的匹配点
        """
        # 准备输入数据
        data = self._prepare_matching_data(image_a, image_b)
        
        # 执行匹配
        with torch.no_grad():
            if self.mixed_precision and self.device.type == 'cuda':
                with torch.cuda.amp.autocast():
                    pred = self.matcher(data)
            else:
                pred = self.matcher(data)
        
        # 提取匹配结果
        matches0 = pred['matches0'][0].cpu().numpy()
        valid = matches0 > -1
        
        mkpts0 = image_a.keypoints[valid]
        mkpts1 = image_b.keypoints[matches0[valid]]
        
        return mkpts0, mkpts1

    def _prepare_matching_data(self, image_a, image_b):
        """准备匹配所需的数据"""
        # 准备图像张量
        tensor_a = self._prepare_tensor(image_a.image)
        tensor_b = self._prepare_tensor(image_b.image)
        
        # 准备特征数据
        keypoints0 = torch.from_numpy(image_a.keypoints).unsqueeze(0).float().to(self.device)
        descriptors0 = torch.from_numpy(image_a.features).unsqueeze(0).float().to(self.device)
        scores0 = torch.ones((1, image_a.keypoints.shape[0]), device=self.device)
        
        keypoints1 = torch.from_numpy(image_b.keypoints).unsqueeze(0).float().to(self.device)
        descriptors1 = torch.from_numpy(image_b.features).unsqueeze(0).float().to(self.device)
        scores1 = torch.ones((1, image_b.keypoints.shape[0]), device=self.device)
        
        # 混合精度优化
        if self.mixed_precision and self.device.type == 'cuda':
            descriptors0 = descriptors0.half()
            descriptors1 = descriptors1.half()
        
        return {
            'image0': tensor_a,
            'keypoints0': keypoints0,
            'descriptors0': descriptors0,
            'scores0': scores0,
            'image1': tensor_b,
            'keypoints1': keypoints1,
            'descriptors1': descriptors1,
            'scores1': scores1,
        }

    def _prepare_tensor(self, img):
        """将OpenCV图像转换为PyTorch张量"""
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        gray3 = np.stack([gray]*3, axis=-1)
        tensor = torch.from_numpy(gray3).float().permute(2, 0, 1).unsqueeze(0) / 255.0
        tensor = tensor.to(self.device)
        
        if self.mixed_precision and self.device.type == 'cuda':
            tensor = tensor.half()
            
        return tensor

    def batch_match(self, image_pairs):
        """
        批量匹配多对图像，提高GPU利用率
        
        Args:
            image_pairs: 图像对列表 [(image_a1, image_b1), (image_a2, image_b2), ...]
            
        Returns:
            matches: 匹配结果列表
        """
        if not image_pairs:
            return []
            
        results = []
        
        # 目前IMP不支持真正的批量处理，所以逐对处理但优化GPU内存使用
        for image_a, image_b in image_pairs:
            mkpts0, mkpts1 = self.match(image_a, image_b)
            results.append((mkpts0, mkpts1))
            
            # 定期清理GPU内存
            if len(results) % 10 == 0 and self.device.type == 'cuda':
                torch.cuda.empty_cache()
        
        return results

    def get_memory_usage(self):
        """获取GPU内存使用情况"""
        if self.device.type == 'cuda':
            return {
                'allocated': torch.cuda.memory_allocated(self.device) / 1024**2,  # MB
                'cached': torch.cuda.memory_reserved(self.device) / 1024**2,      # MB
                'max_allocated': torch.cuda.max_memory_allocated(self.device) / 1024**2,  # MB
            }
        return {'allocated': 0, 'cached': 0, 'max_allocated': 0}

    def optimize_memory(self):
        """优化GPU内存使用"""
        if self.device.type == 'cuda':
            torch.cuda.empty_cache()
            torch.cuda.synchronize()

    def __del__(self):
        """析构函数，清理GPU资源"""
        if hasattr(self, 'device') and self.device.type == 'cuda':
            torch.cuda.empty_cache()
