import cv2
import numpy as np

def create_geometric_mask(image_shape, margin=50):
    """
    创建基于几何形状的统一掩码
    避免边缘区域，确保所有图像使用相同的有效区域
    """
    h, w = image_shape[:2]
    
    # 创建矩形掩码（去除边缘）
    mask = np.zeros((h, w), dtype=bool)
    mask[margin:h-margin, margin:w-margin] = True
    
    print(f"几何掩码信息:")
    print(f"  原始图像尺寸: {w}x{h}")
    print(f"  边缘裕度: {margin}像素")
    print(f"  有效区域尺寸: {w-2*margin}x{h-2*margin}")
    print(f"  有效像素数: {np.sum(mask)}")
    
    return mask

def create_circular_mask(image_shape, radius_ratio=0.8):
    """
    创建圆形掩码
    """
    h, w = image_shape[:2]
    center_x, center_y = w // 2, h // 2
    radius = min(center_x, center_y) * radius_ratio
    
    y, x = np.ogrid[:h, :w]
    mask = (x - center_x)**2 + (y - center_y)**2 <= radius**2
    
    print(f"圆形掩码信息:")
    print(f"  中心: ({center_x}, {center_y})")
    print(f"  半径: {radius:.1f}")
    print(f"  有效像素数: {np.sum(mask)}")
    
    return mask

def apply_mask_and_stitch(img1_path, img2_path, H, mask, output_name):
    """
    应用掩码并进行拼接
    """
    
    # 读取图像
    img1 = cv2.imread(img1_path)
    img2 = cv2.imread(img2_path)
    
    # 应用掩码
    img1_masked = img1.copy()
    img2_masked = img2.copy()
    
    if len(img1.shape) == 3:
        img1_masked[~mask] = [0, 0, 0]
        img2_masked[~mask] = [0, 0, 0]
    else:
        img1_masked[~mask] = 0
        img2_masked[~mask] = 0
    
    # 计算变换
    h1, w1 = img1_masked.shape[:2]
    h2, w2 = img2_masked.shape[:2]
    
    corners = np.float32([[0, 0], [w2, 0], [w2, h2], [0, h2]]).reshape(-1, 1, 2)
    transformed_corners = cv2.perspectiveTransform(corners, H)
    
    all_corners = np.vstack([
        [[0, 0], [w1, 0], [w1, h1], [0, h1]],
        transformed_corners.reshape(-1, 2)
    ])
    
    x_min, y_min = np.min(all_corners, axis=0)
    x_max, y_max = np.max(all_corners, axis=0)
    
    new_width = int(x_max - x_min)
    new_height = int(y_max - y_min)
    
    translation = np.array([
        [1, 0, -x_min],
        [0, 1, -y_min],
        [0, 0, 1]
    ])
    
    # 变换图像
    result_img1 = cv2.warpPerspective(img1_masked, translation, (new_width, new_height))
    combined_H = np.dot(translation, H)
    result_img2 = cv2.warpPerspective(img2_masked, combined_H, (new_width, new_height))
    
    # 计算重叠区域
    mask1 = (result_img1.sum(axis=2) > 0)
    mask2 = (result_img2.sum(axis=2) > 0)
    overlap = mask1 & mask2
    
    # 融合
    final_result = np.zeros_like(result_img1)
    final_result[mask1 & ~overlap] = result_img1[mask1 & ~overlap]
    final_result[mask2 & ~overlap] = result_img2[mask2 & ~overlap]
    
    if np.any(overlap):
        img1_overlap = result_img1[overlap].astype(np.float32)
        img2_overlap = result_img2[overlap].astype(np.float32)
        
        gray1 = 0.114 * img1_overlap[:, 0] + 0.587 * img1_overlap[:, 1] + 0.299 * img1_overlap[:, 2]
        gray2 = 0.114 * img2_overlap[:, 0] + 0.587 * img2_overlap[:, 1] + 0.299 * img2_overlap[:, 2]
        
        epsilon = 1e-6
        weight1 = gray1 + epsilon
        weight2 = gray2 + epsilon
        
        total_weight = weight1 + weight2
        weight1_norm = weight1 / total_weight
        weight2_norm = weight2 / total_weight
        
        blended = np.zeros_like(img1_overlap)
        for c in range(3):
            blended[:, c] = (img1_overlap[:, c] * weight1_norm + 
                            img2_overlap[:, c] * weight2_norm)
        
        final_result[overlap] = blended.astype(np.uint8)
    
    cv2.imwrite(output_name, final_result)
    
    return np.sum(overlap) if np.any(overlap) else 0

def main():
    print("🔍 几何掩码拼接测试")
    print("="*60)
    
    # 读取一张图像获取尺寸
    sample_img = cv2.imread("D:/daima/gitup/7month/image-stitching-main/testdata3/1.bmp")
    
    # 测试不同的掩码类型
    masks = {
        "rectangular_50": create_geometric_mask(sample_img.shape, margin=50),
        "rectangular_100": create_geometric_mask(sample_img.shape, margin=100),
        "circular_80": create_circular_mask(sample_img.shape, radius_ratio=0.8),
        "circular_60": create_circular_mask(sample_img.shape, radius_ratio=0.6)
    }
    
    H = np.array([
        [1.118417764482483, 0.0603628696196226, 948.2571340071909],
        [-0.0603628696196226, 1.118417764482483, -66.74286599280914],
        [0.0, 0.0, 1.0]
    ])
    
    results = {}
    
    for mask_name, mask in masks.items():
        print(f"\n{'='*40}")
        print(f"测试掩码: {mask_name}")
        print(f"{'='*40}")
        
        overlap1 = apply_mask_and_stitch(
            "D:/daima/gitup/7month/image-stitching-main/testdata3/2.bmp",
            "D:/daima/gitup/7month/image-stitching-main/testdata3/4.bmp",
            H, mask, f"geometric_2_4_{mask_name}.bmp"
        )
        
        overlap2 = apply_mask_and_stitch(
            "D:/daima/gitup/7month/image-stitching-main/testdata3/1.bmp",
            "D:/daima/gitup/7month/image-stitching-main/testdata3/3.bmp",
            H, mask, f"geometric_1_3_{mask_name}.bmp"
        )
        
        results[mask_name] = {
            'overlap1': overlap1,
            'overlap2': overlap2,
            'diff': abs(overlap1 - overlap2)
        }
        
        print(f"2+4重叠像素: {overlap1}")
        print(f"1+3重叠像素: {overlap2}")
        print(f"差异: {abs(overlap1 - overlap2)}")
    
    print(f"\n{'='*60}")
    print("所有掩码测试结果:")
    print(f"{'='*60}")
    
    for mask_name, result in results.items():
        status = "✅ 完全相同" if result['diff'] == 0 else f"差异: {result['diff']}"
        print(f"{mask_name:20s}: {status}")
    
    # 找出最佳掩码
    best_mask = min(results.keys(), key=lambda k: results[k]['diff'])
    print(f"\n🏆 最佳掩码: {best_mask} (差异: {results[best_mask]['diff']})")

if __name__ == "__main__":
    main()
