from PIL import Image
import os

# 裁剪配置
crop_horizontal = True  # 是否裁剪水平方向
crop_vertical = False    # 是否裁剪垂直方向

# 水平裁剪参数（像素）
crop_left = 304

crop_right = 304

# 垂直裁剪参数（像素）
crop_top = 50
crop_bottom = 75

input_directory = 'F:/daman/0701/scan_0701_211839/yangdai/photo_3D_75ns_crop/2'
output_directory = 'F:/daman/0701/scan_0701_211839/yangdai/photo_3D_75ns_crop/2/1'

os.makedirs(output_directory, exist_ok=True)

bmp_files = sorted([f for f in os.listdir(input_directory) if f.endswith('.bmp')], key=lambda x: int(x.split('.')[0]))

for img_file in bmp_files:
    img_path = os.path.join(input_directory, img_file)
    img = Image.open(img_path)

    left = 0
    right = img.width
    top = 0
    bottom = img.height

    if crop_horizontal:
        left = crop_left
        right = img.width - crop_right
        # 确保不会裁剪越界
        left = max(0, min(left, img.width))
        right = max(left, min(right, img.width))

    if crop_vertical:
        top = crop_top
        bottom = img.height - crop_bottom
        top = max(0, min(top, img.height))
        bottom = max(top, min(bottom, img.height))

    cropped_img = img.crop((left, top, right, bottom))

    output_image = os.path.join(output_directory, img_file)
    cropped_img.save(output_image)

    print(f"{img_file} 已裁剪并保存至 {output_image}")
