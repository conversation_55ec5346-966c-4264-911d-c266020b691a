name: Pip
on:
  workflow_dispatch:
  pull_request:
  push:
    branches:
      - main

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        platform: [ubuntu-latest]
        python-version: ["3.9", "3.10"]

    runs-on: ${{ matrix.platform }}
    # runs-on: self-hosted
    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive

    - uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Upgrade setuptools and wheel
      run: |
        pip install --upgrade setuptools wheel

    - name: Install dependencies on Ubuntu
      if: runner.os == 'Linux'
      run: |
        sudo apt-get update
        sudo apt-get install libopencv-dev -y

    - name: Install dependencies on macOS
      if: runner.os == 'macOS'
      run: |
        brew update
        brew install opencv

    - name: Install dependencies on Windows
      if: runner.os == 'Windows'
      run: |
        choco install opencv -y

    - name: Add requirements
      run: python -m pip install --upgrade wheel setuptools

    - name: Install Python dependencies
      run: |
        pip install pytest
        pip install -r requirements.txt
        sudo apt-get update && sudo apt-get install ffmpeg libsm6 libxext6 -y

    - name: Build and install
      run: pip install .

    - name: Test
      run: python -m pytest
