#!/usr/bin/env python3
"""
GPU vs CPU 性能对比脚本
"""

import argparse
import time
import torch
from pathlib import Path

def benchmark_cpu_vs_gpu(data_dir: Path, size: int = 1000, count: int = 6):
    """
    对比CPU和GPU版本的性能
    
    Args:
        data_dir: 图像目录
        size: 图像缩放尺寸
        count: 测试图像数量
    """
    # 检查CUDA可用性
    cuda_available = torch.cuda.is_available()
    if cuda_available:
        gpu_name = torch.cuda.get_device_name()
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"🚀 检测到GPU: {gpu_name} ({gpu_memory:.1f} GB)")
    else:
        print("⚠️  未检测到CUDA GPU，将只测试CPU版本")
    
    # 准备测试数据
    valid_exts = {".jpg", ".png", ".bmp", ".jpeg"}
    image_paths = sorted([
        str(p) for p in data_dir.iterdir()
        if p.suffix.lower() in valid_exts
    ])[:count]

    if len(image_paths) < 2:
        print("需要至少两张图像进行测试")
        return

    print(f"📸 使用 {len(image_paths)} 张图像进行测试")
    print("图像列表:")
    for i, path in enumerate(image_paths):
        print(f"  {i}: {Path(path).name}")
    print()

    # 测试CPU版本
    print("=" * 60)
    print("🖥️  CPU版本性能测试")
    print("=" * 60)
    
    cpu_time = test_cpu_version(image_paths, size)
    
    # 测试GPU版本（如果可用）
    if cuda_available:
        print("\n" + "=" * 60)
        print("🚀 GPU版本性能测试")
        print("=" * 60)
        
        gpu_time = test_gpu_version(image_paths, size)
        
        # 性能对比
        print("\n" + "=" * 60)
        print("📊 性能对比结果")
        print("=" * 60)
        print(f"CPU版本总耗时:  {cpu_time:.2f}s")
        print(f"GPU版本总耗时:  {gpu_time:.2f}s")
        
        if gpu_time > 0:
            speedup = cpu_time / gpu_time
            print(f"GPU加速倍数:    {speedup:.1f}x")
            
            if speedup > 2:
                print("🎉 GPU加速效果显著！")
            elif speedup > 1.2:
                print("✅ GPU加速有一定效果")
            else:
                print("⚠️  GPU加速效果不明显，可能受限于数据传输开销")
    else:
        print("\n⚠️  跳过GPU测试（CUDA不可用）")

def test_cpu_version(image_paths, size):
    """测试CPU版本"""
    try:
        from src.images import Image
        from src.matching.imp_matcher import IMPMatcher
        from src.matching import MultiImageMatches
        
        start_time = time.time()
        
        # 加载图像和特征提取
        print("1. 加载图像并提取特征...")
        feature_start = time.time()
        images = [Image(path, size) for path in image_paths]
        for img in images:
            img.compute_features()
        feature_time = time.time() - feature_start
        print(f"   特征提取耗时: {feature_time:.2f}s")
        
        # 特征匹配
        print("2. 特征匹配...")
        match_start = time.time()
        matcher = IMPMatcher()
        match_graph = MultiImageMatches(images, matcher, sequential_matching=True)
        pair_matches = match_graph.get_pair_matches()
        match_time = time.time() - match_start
        print(f"   特征匹配耗时: {match_time:.2f}s")
        print(f"   找到 {len(pair_matches)} 个有效匹配对")
        
        total_time = time.time() - start_time
        print(f"CPU版本总耗时: {total_time:.2f}s")
        
        return total_time
        
    except Exception as e:
        print(f"❌ CPU版本测试失败: {e}")
        return float('inf')

def test_gpu_version(image_paths, size):
    """测试GPU版本"""
    try:
        from src.images.image_gpu import ImageGPU
        from src.matching.imp_matcher_gpu import IMPMatcherGPU
        from src.matching.multi_images_matches_gpu import MultiImageMatchesGPU
        
        device = torch.device('cuda')
        start_time = time.time()
        
        # 加载图像和特征提取
        print("1. 加载图像并提取特征...")
        feature_start = time.time()
        images = [ImageGPU(path, size, device=device) for path in image_paths]
        
        # 批量特征提取
        ImageGPU.batch_compute_features(images, mixed_precision=True)
        
        feature_time = time.time() - feature_start
        print(f"   特征提取耗时: {feature_time:.2f}s")
        
        # GPU内存使用情况
        if device.type == 'cuda':
            memory_allocated = torch.cuda.memory_allocated(device) / 1024**2
            print(f"   GPU内存使用: {memory_allocated:.0f}MB")
        
        # 特征匹配
        print("2. 特征匹配...")
        match_start = time.time()
        matcher = IMPMatcherGPU(device=device, mixed_precision=True)
        match_graph = MultiImageMatchesGPU(images, matcher, sequential_matching=True)
        pair_matches = match_graph.get_pair_matches()
        match_time = time.time() - match_start
        print(f"   特征匹配耗时: {match_time:.2f}s")
        print(f"   找到 {len(pair_matches)} 个有效匹配对")
        
        total_time = time.time() - start_time
        print(f"GPU版本总耗时: {total_time:.2f}s")
        
        # 清理GPU内存
        torch.cuda.empty_cache()
        
        return total_time
        
    except Exception as e:
        print(f"❌ GPU版本测试失败: {e}")
        import traceback
        traceback.print_exc()
        return float('inf')

def main():
    parser = argparse.ArgumentParser(description="CPU vs GPU 性能对比")
    parser.add_argument("data_dir", type=Path, help="图像目录")
    parser.add_argument("--size", type=int, default=1000, help="图像缩放尺寸")
    parser.add_argument("--count", type=int, default=6, help="测试图像数量")
    
    args = parser.parse_args()
    
    if not args.data_dir.exists():
        print(f"错误: 目录 {args.data_dir} 不存在")
        return
    
    benchmark_cpu_vs_gpu(args.data_dir, args.size, args.count)

if __name__ == "__main__":
    main()
