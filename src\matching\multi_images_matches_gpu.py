import cv2
import numpy as np
import re
import torch
from pathlib import Path

from src.images.image_gpu import ImageGPU
from src.matching.pair_match import PairMatch


def natural_sort_key(path_str: str):
    """
    生成用于自然排序的键，使得数字按数值大小排序而不是字符串排序
    例如: 1.bmp, 2.bmp, 10.bmp 而不是 1.bmp, 10.bmp, 2.bmp
    """
    filename = Path(path_str).stem  # 获取不带扩展名的文件名
    # 将文件名中的数字部分转换为整数进行排序
    parts = re.split(r'(\d+)', filename)
    result = []
    for part in parts:
        if part.isdigit():
            result.append(int(part))
        else:
            result.append(part)
    return result


class MultiImageMatchesGPU:
    """GPU优化的多图像匹配类"""
    
    def __init__(self, images: list[ImageGPU], matcher, ratio: float = 0.75, 
                 sequential_matching: bool = False, seq_range: int = 1) -> None:
        """
        Create a new MultiImageMatchesGPU object.

        Args:
            images: images to compare (ImageGPU objects)
            matcher: GPU feature matcher to use (e.g., IMPMatcherGPU)
            ratio: ratio used for the Lowe's ratio test
            sequential_matching: if True, only match adjacent images based on filename order
            seq_range: range for sequential matching (1=adjacent only, 2=adjacent+skip-one, etc.)
        """
        self.images = images
        self.matches = {image.path: {} for image in images}
        self.matcher = matcher
        self.ratio = ratio
        self.sequential_matching = sequential_matching
        self.seq_range = seq_range
        
        # GPU内存监控
        self.device = matcher.device
        print(f"🔧 多图像匹配器初始化 (设备: {self.device})")

    def get_matches(self, image_a: ImageGPU, image_b: ImageGPU) -> list:
        """
        Get matches for the given images.

        Args:
            image_a: First image
            image_b: Second image

        Returns:
            matches: List of matches between the two images
        """
        if image_b.path not in self.matches[image_a.path]:
            matches = self.compute_matches(image_a, image_b)
            self.matches[image_a.path][image_b.path] = matches

        return self.matches[image_a.path][image_b.path]

    def get_pair_matches(self, max_images: int = 6) -> list[PairMatch]:
        """
        Get the pair matches for the given images.

        Args:
            max_images: Number of matches maximum for each image

        Returns:
            pair_matches: List of pair matches
        """
        if self.sequential_matching:
            return self._get_sequential_pair_matches()
        else:
            return self._get_all_pair_matches(max_images)
    
    def _get_sequential_pair_matches(self) -> list[PairMatch]:
        """
        Get pair matches for sequential images based on filename order.
        GPU优化版本，支持内存监控和批量处理。
        
        Returns:
            pair_matches: List of pair matches for adjacent images
        """
        pair_matches = []
        print(f"🚀 使用GPU顺序匹配，处理 {len(self.images)} 张图像，范围 {self.seq_range}...")
        
        # Sort images by filename using natural sorting (numeric order)
        sorted_images = sorted(self.images, key=lambda img: natural_sort_key(img.path))
        
        print("📋 图像排序结果:")
        for i, img in enumerate(sorted_images):
            print(f"  {i}: {Path(img.path).name}")
        print()
        
        # 收集所有需要匹配的图像对
        image_pairs = []
        for i in range(len(sorted_images)):
            for j in range(1, min(self.seq_range + 1, len(sorted_images) - i)):
                image_pairs.append((sorted_images[i], sorted_images[i + j], j))
        
        print(f"🔄 总共需要匹配 {len(image_pairs)} 对图像")
        
        # 逐对匹配（支持GPU加速）
        for idx, (image_a, image_b, gap) in enumerate(image_pairs):
            print(f"[{idx+1}/{len(image_pairs)}] 匹配 {Path(image_a.path).name} ↔ {Path(image_b.path).name} (间隔: {gap})")
            
            # 监控GPU内存
            if self.device.type == 'cuda' and idx % 5 == 0:
                memory_info = self.matcher.get_memory_usage()
                print(f"   GPU内存: {memory_info['allocated']:.0f}MB 已分配, {memory_info['cached']:.0f}MB 已缓存")
            
            matches = self.get_matches(image_a, image_b)
            
            if len(matches) > 0:
                pair_match = PairMatch(image_a, image_b, matches)
                if pair_match.is_valid():
                    pair_matches.append(pair_match)
                    print(f"   ✅ 有效匹配: {len(matches)} 个特征匹配")
                else:
                    print(f"   ❌ 无效匹配 (内点不足)")
            else:
                print(f"   ⚠️  未找到特征匹配")
            
            # 定期清理GPU内存
            if idx % 10 == 9 and self.device.type == 'cuda':
                self.matcher.optimize_memory()
        
        print(f"🎉 顺序匹配完成，找到 {len(pair_matches)} 个有效匹配对")
        return pair_matches
    
    def _get_all_pair_matches(self, max_images: int = 6) -> list[PairMatch]:
        """
        Get pair matches using the original all-to-all matching strategy.
        GPU优化版本。
        
        Args:
            max_images: Number of matches maximum for each image
            
        Returns:
            pair_matches: List of pair matches
        """
        print(f"🚀 使用GPU全匹配策略，处理 {len(self.images)} 张图像...")
        
        pair_matches = []
        total_pairs = len(self.images) * (len(self.images) - 1) // 2
        current_pair = 0
        
        for i, image_a in enumerate(self.images):
            possible_matches = sorted(
                self.images[:i] + self.images[i + 1 :],
                key=lambda image, ref=image_a: len(self.get_matches(ref, image)),
                reverse=True,
            )[:max_images]
            
            for image_b in possible_matches:
                if self.images.index(image_b) > i:
                    current_pair += 1
                    print(f"[{current_pair}/{total_pairs}] 匹配 {Path(image_a.path).name} ↔ {Path(image_b.path).name}")
                    
                    pair_match = PairMatch(image_a, image_b, self.get_matches(image_a, image_b))
                    if pair_match.is_valid():
                        pair_matches.append(pair_match)
                        print(f"   ✅ 有效匹配")
                    else:
                        print(f"   ❌ 无效匹配")
                    
                    # 定期清理GPU内存
                    if current_pair % 10 == 0 and self.device.type == 'cuda':
                        self.matcher.optimize_memory()
        
        print(f"🎉 全匹配完成，找到 {len(pair_matches)} 个有效匹配对")
        return pair_matches

    def compute_matches(self, image_a: ImageGPU, image_b: ImageGPU) -> list:
        """
        Compute matches between image_a and image_b using GPU acceleration.

        Args:
            image_a: First image
            image_b: Second image

        Returns:
            matches: List of matches between image_a and image_b
        """
        # Use the GPU matcher
        mkpts0, mkpts1 = self.matcher.match(image_a, image_b)
        
        # Convert matched keypoints to cv2.DMatch format for compatibility
        matches = []
        for i in range(len(mkpts0)):
            # Find indices of matched keypoints in original keypoint arrays
            idx0 = self._find_keypoint_index(image_a.keypoints, mkpts0[i])
            idx1 = self._find_keypoint_index(image_b.keypoints, mkpts1[i])
            
            if idx0 is not None and idx1 is not None:
                # Create DMatch object for compatibility with existing code
                match = cv2.DMatch(idx0, idx1, 0.0)  # distance set to 0 since IMP already filtered
                matches.append(match)
        
        return matches
    
    def _find_keypoint_index(self, keypoints: np.ndarray, target_point: np.ndarray) -> int:
        """
        Find the index of a keypoint in the keypoints array.
        
        Args:
            keypoints: Array of keypoints
            target_point: Target keypoint to find
            
        Returns:
            Index of the keypoint, or None if not found
        """
        # For SFD2, keypoints are stored as [N, 2] numpy array
        distances = np.linalg.norm(keypoints - target_point, axis=1)
        min_idx = np.argmin(distances)
        
        # Check if the closest point is actually the same (within small tolerance)
        if distances[min_idx] < 1e-6:
            return min_idx
        return None

    def get_memory_usage(self):
        """获取GPU内存使用情况"""
        return self.matcher.get_memory_usage()

    def optimize_memory(self):
        """优化GPU内存使用"""
        self.matcher.optimize_memory()
