# To use:
#
#     pre-commit run -a
#
# Or:
#
#     pre-commit run --all-files
#
# Or:
#
#     pre-commit install  # (runs every time you commit in git)
#
# To update this file:
#
#     pre-commit autoupdate
#
# See https://github.com/pre-commit/pre-commit

ci:
  autoupdate_commit_msg: "chore: update pre-commit hooks"
  autofix_commit_msg: "style: pre-commit fixes"

repos:
# Standard hooks
- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v5.0.0
  hooks:
  - id: check-added-large-files
    exclude: ^imcui/third_party/
  - id: check-case-conflict
    exclude: ^imcui/third_party/
  - id: check-merge-conflict
    exclude: ^imcui/third_party/
  - id: check-symlinks
    exclude: ^imcui/third_party/
  - id: check-yaml
    exclude: ^imcui/third_party/
  - id: debug-statements
    exclude: ^imcui/third_party/
  - id: end-of-file-fixer
    exclude: ^imcui/third_party/
  - id: mixed-line-ending
    exclude: ^imcui/third_party/
  - id: requirements-txt-fixer
    exclude: ^imcui/third_party/
  - id: trailing-whitespace
    exclude: ^imcui/third_party/

- repo: https://github.com/astral-sh/ruff-pre-commit
  rev: "v0.8.4"
  hooks:
    - id: ruff
      args: ["--fix", "--show-fixes", "--extend-ignore=E402"]
    - id: ruff-format
      exclude: ^(docs|imcui/third_party/)

# Checking static types
- repo: https://github.com/pre-commit/mirrors-mypy
  rev: "v1.14.0"
  hooks:
    - id: mypy
      files: "setup.py"
      args: []
      additional_dependencies: [types-setuptools]
      exclude: ^imcui/third_party/
# Changes tabs to spaces
- repo: https://github.com/Lucas-C/pre-commit-hooks
  rev: v1.5.5
  hooks:
  - id: remove-tabs
    exclude: ^(docs|imcui/third_party/)

# CMake formatting
- repo: https://github.com/cheshirekow/cmake-format-precommit
  rev: v0.6.13
  hooks:
  - id: cmake-format
    additional_dependencies: [pyyaml]
    types: [file]
    files: (\.cmake|CMakeLists.txt)(.in)?$
    exclude: ^imcui/third_party/

# Suggested hook if you add a .clang-format file
- repo: https://github.com/pre-commit/mirrors-clang-format
  rev: v13.0.0
  hooks:
  - id: clang-format
    exclude: ^imcui/third_party/
