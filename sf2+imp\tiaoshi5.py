#计算单映性矩阵，再进行转化。
from imcui.hloc.extractors.sfd2 import SFD2
from imcui.hloc.matchers.imp import IMP
import torch
import numpy as np
import cv2
from PIL import Image
import os

# 加载特征提取器与匹配器
sfd2_extractor = SFD2({'max_keypoints': 4096, 'conf_th': 0.001})
imp_matcher = IMP({'match_threshold': 0.2, 'features': 'sfd2'})

def load_gray_image_unicode(path, resize_scale=1.0):
    img = Image.open(path).convert('L')
    if resize_scale != 1.0:
        new_size = (int(img.width * resize_scale), int(img.height * resize_scale))
        img = img.resize(new_size, Image.BILINEAR)
    return np.array(img)

def preprocess_gray(image):
    image = np.stack([image]*3, axis=-1)
    return torch.from_numpy(image).float().permute(2, 0, 1).unsqueeze(0) / 255.0

def compute_homography(img1, img2):
    tensor1 = preprocess_gray(img1)
    tensor2 = preprocess_gray(img2)

    pred1 = sfd2_extractor({'image': tensor1})
    pred2 = sfd2_extractor({'image': tensor2})

    matches = imp_matcher({
        'image0': tensor1,
        'keypoints0': pred1['keypoints'],
        'scores0': pred1['scores'],
        'descriptors0': pred1['descriptors'],
        'image1': tensor2,
        'keypoints1': pred2['keypoints'],
        'scores1': pred2['scores'],
        'descriptors1': pred2['descriptors'],
    })

    kp1 = pred1['keypoints'][0].cpu().numpy()
    kp2 = pred2['keypoints'][0].cpu().numpy()

    if 'matches0' in matches:
        matches0 = matches['matches0'][0].cpu().numpy()
        valid = matches0 > -1
        mkpts1 = kp1[valid]
        mkpts2 = kp2[matches0[valid]]
    else:
        print("匹配失败")
        return None, None

    if len(mkpts1) >= 4:
        H, mask = cv2.findHomography(mkpts1, mkpts2, cv2.RANSAC, 3.0)
        if H is None:
            print("计算单应矩阵失败")
            return None, None
        return H, mask
    else:
        print("匹配点不足")
        return None, None

def stitch_all_images(images, homographies, output_dir):
    h0, w0 = images[0].shape[:2]
    corners = np.array([[0, 0], [0, h0], [w0, h0], [w0, 0]], dtype=np.float32).reshape(-1,1,2)

    all_corners = []

    for i, img in enumerate(images):
        h, w = img.shape[:2]
        img_corners = np.array([[0, 0], [0, h], [w, h], [w, 0]], dtype=np.float32).reshape(-1,1,2)
        if i == 0:
            transformed = img_corners
        else:
            transformed = cv2.perspectiveTransform(img_corners, homographies[i])
        all_corners.append(transformed)

    all_corners = np.concatenate(all_corners, axis=0)

    [xmin, ymin] = np.floor(np.min(all_corners, axis=0).ravel()).astype(int)
    [xmax, ymax] = np.ceil(np.max(all_corners, axis=0).ravel()).astype(int)

    translation = np.array([[1, 0, -xmin],
                            [0, 1, -ymin],
                            [0, 0, 1]])

    canvas_width = xmax - xmin
    canvas_height = ymax - ymin

    canvas = np.zeros((canvas_height, canvas_width), dtype=np.uint8)

    for i, img in enumerate(images):
        if i == 0:
            H = translation
        else:
            H = translation @ homographies[i]

        H = np.array(H, dtype=np.float64)  # 强制转换类型，避免OpenCV错误

        warped = cv2.warpPerspective(img, H, (canvas_width, canvas_height))
        mask = (warped > 0).astype(np.float32)

        canvas_mask = (canvas > 0).astype(np.float32)
        overlap = (mask > 0) & (canvas_mask > 0)

        canvas = np.where(overlap, (canvas.astype(np.float32) + warped.astype(np.float32)) / 2, canvas + warped).astype(np.uint8)

    save_path = os.path.join(output_dir, 'final_stitched.png')
    cv2.imwrite(save_path, canvas)
    print(f"拼接完成，结果保存在：{save_path}")

def main():
    folder = r'G:photo_2D_80ns_A'
    file_template = "{}.bmp"
    start_idx = 1
    end_idx = 6  # 包括第6张图

    output_dir = os.path.join(folder, 'pinjie')
    os.makedirs(output_dir, exist_ok=True)

    images = []
    for i in range(start_idx, end_idx + 1):
        path = os.path.join(folder, file_template.format(i))
        img = load_gray_image_unicode(path, resize_scale=1.0)
        images.append(img)

    homographies = [np.eye(3, dtype=np.float64)]
    for i in range(len(images) - 1):
        print(f"计算第{i+1}和{i+2}张图的单应矩阵...")
        H, mask = compute_homography(images[i], images[i+1])
        if H is None:
            print(f"第{i+1}和{i+2}张图单应矩阵计算失败，终止程序。")
            return
        homographies.append(H.astype(np.float64))

    # 从第3个开始累积变换，保证都是相对于第一张图的单应矩阵
    for i in range(2, len(homographies)):
        homographies[i] = homographies[i-1] @ homographies[i]
        homographies[i] = np.array(homographies[i], dtype=np.float64)

    stitch_all_images(images, homographies, output_dir)

if __name__ == "__main__":
    main()

