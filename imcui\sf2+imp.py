# from imcui.hloc.extractors.sfd2 import SFD2
# from imcui.hloc.matchers.imp import IMP
# import cv2
# import torch
# import numpy as np
# import matplotlib.pyplot as plt

# # 加载特征提取器
# sfd2_extractor = SFD2({'max_keypoints': 4096, 'conf_th': 0.001})

# # 加载匹配器
# imp_matcher = IMP({'match_threshold': 0.2, 'features': 'sfd2'})

# # 读取图像
# image0 = cv2.imread('D:/data/20240910yanshan/huidu/A/yangdai2_2.bmp')
# image1 = cv2.imread('D:/data/20240910yanshan/huidu/A/yangdai2_3.bmp')

# # 检查图像是否成功加载
# if image0 is None or image1 is None:
#     print("无法读取图像，请检查路径是否正确")
#     # 使用示例图像路径
#     print("请修改代码中的图像路径为你的实际图像路径")
#     exit()

# image0 = cv2.cvtColor(image0, cv2.COLOR_BGR2RGB)
# image1 = cv2.cvtColor(image1, cv2.COLOR_BGR2RGB)

# # 提取特征 - 注意这里需要调整图像维度
# # 从[H, W, C]转换为[1, C, H, W]格式
# image0_tensor = torch.from_numpy(image0).float().permute(2, 0, 1).unsqueeze(0)
# image1_tensor = torch.from_numpy(image1).float().permute(2, 0, 1).unsqueeze(0)

# pred0 = sfd2_extractor({'image': image0_tensor})
# pred1 = sfd2_extractor({'image': image1_tensor})

# # 特征匹配
# matches = imp_matcher({
#     'image0': image0_tensor,
#     'keypoints0': pred0['keypoints'],
#     'scores0': pred0['scores'],
#     'descriptors0': pred0['descriptors'],
#     'image1': image1_tensor,
#     'keypoints1': pred1['keypoints'],
#     'scores1': pred1['scores'],
#     'descriptors1': pred1['descriptors'],
# })

# # 打印匹配结果的键，以便了解返回的数据结构
# print("匹配结果包含的键:", matches.keys())

# # 提取匹配点
# keypoints0 = pred0['keypoints'][0].cpu().numpy()
# keypoints1 = pred1['keypoints'][0].cpu().numpy()

# # 获取匹配结果 - 根据实际返回的键进行处理
# if 'matches0' in matches:
#     # 如果返回的是matches0格式
#     matches0 = matches['matches0'][0].cpu().numpy()  # 添加[0]索引
#     valid = matches0 > -1
#     mkpts0 = keypoints0[valid]
#     mkpts1 = keypoints1[matches0[valid]]
# elif 'keypoints0' in matches and 'keypoints1' in matches:
#     # 如果返回的是直接的匹配点
#     mkpts0 = matches['keypoints0'].cpu().numpy()
#     mkpts1 = matches['keypoints1'].cpu().numpy()
# else:
#     # 如果是其他格式，尝试查找其他可能的键
#     print("未找到标准匹配点格式，尝试其他可能的键...")
#     if 'mkpts0' in matches and 'mkpts1' in matches:
#         mkpts0 = matches['mkpts0'].cpu().numpy()
#         mkpts1 = matches['mkpts1'].cpu().numpy()
#     elif 'mkeypoints0' in matches and 'mkeypoints1' in matches:
#         mkpts0 = matches['mkeypoints0'].cpu().numpy()
#         mkpts1 = matches['mkeypoints1'].cpu().numpy()
#     else:
#         # 如果实在找不到匹配点，创建空数组
#         print("无法找到匹配点，请检查匹配器返回的数据结构")
#         mkpts0 = np.array([])
#         mkpts1 = np.array([])

# print(f"找到 {len(mkpts0)} 对匹配点")

# # 计算单应性矩阵
# if len(mkpts0) >= 4:
#     H, mask = cv2.findHomography(mkpts0, mkpts1, cv2.RANSAC, 3.0)
#     print("单应性矩阵 (Homography):")
#     print(H)
    
#     # 计算内点数量
#     inliers = np.sum(mask)
#     print(f"匹配点数量: {len(mkpts0)}, 内点数量: {inliers}")
# else:
#     print("匹配点不足，无法计算单应性矩阵")
#     H = None

# # 可视化匹配结果
# def plot_matches(img1, img2, kpts1, kpts2):
#     # 创建拼接图像
#     h1, w1 = img1.shape[:2]
#     h2, w2 = img2.shape[:2]
#     height = max(h1, h2)
#     width = w1 + w2
#     vis = np.zeros((height, width, 3), np.uint8)
#     vis[:h1, :w1] = img1
#     vis[:h2, w1:w1+w2] = img2
    
#     # 绘制匹配线
#     for pt1, pt2 in zip(kpts1, kpts2):
#         pt1 = tuple(map(int, pt1))
#         pt2 = (int(pt2[0] + w1), int(pt2[1]))
#         cv2.circle(vis, pt1, 3, (0, 255, 0), -1)
#         cv2.circle(vis, pt2, 3, (0, 255, 0), -1)
#         cv2.line(vis, pt1, pt2, (0, 255, 0), 1)
    
#     return vis

# # 只有在有匹配点的情况下才绘制
# if len(mkpts0) > 0:
#     # 绘制匹配结果
#     match_vis = plot_matches(image0, image1, mkpts0, mkpts1)
#     plt.figure(figsize=(20, 10))
#     plt.imshow(match_vis)
#     plt.title(f'SFD2+IMP 匹配结果: {len(mkpts0)} 对匹配点')
#     plt.axis('off')
#     plt.savefig('匹配结果.png', bbox_inches='tight')
#     plt.show()

#     # 如果需要，可以保存匹配结果
#     np.savez('匹配结果.npz', 
#              keypoints0=mkpts0, 
#              keypoints1=mkpts1, 
#              homography=H)

#     print("匹配结果已保存")
# else:
#     print("没有找到匹配点，无法生成可视化结果")


from imcui.hloc.extractors.sfd2 import SFD2
from imcui.hloc.matchers.imp import IMP
import cv2
import torch
import numpy as np
import matplotlib.pyplot as plt

# 加载特征提取器
sfd2_extractor = SFD2({'max_keypoints': 4096, 'conf_th': 0.001})

# 加载匹配器
imp_matcher = IMP({'match_threshold': 0.2, 'features': 'sfd2'})

# 读取图像
image0 = cv2.imread('D:/aa_shanhu/output_frames2/085.jpg')
image1 = cv2.imread('D:/aa_shanhu/output_frames2/086.jpg')

# 检查图像是否成功加载
if image0 is None or image1 is None:
    print("无法读取图像，请检查路径是否正确")
    # 使用示例图像路径
    print("请修改代码中的图像路径为你的实际图像路径")
    exit()

image0 = cv2.cvtColor(image0, cv2.COLOR_BGR2RGB)
image1 = cv2.cvtColor(image1, cv2.COLOR_BGR2RGB)

# 提取特征 - 注意这里需要调整图像维度
# 从[H, W, C]转换为[1, C, H, W]格式
image0_tensor = torch.from_numpy(image0).float().permute(2, 0, 1).unsqueeze(0)
image1_tensor = torch.from_numpy(image1).float().permute(2, 0, 1).unsqueeze(0)

pred0 = sfd2_extractor({'image': image0_tensor})
pred1 = sfd2_extractor({'image': image1_tensor})

# 特征匹配
matches = imp_matcher({
    'image0': image0_tensor,
    'keypoints0': pred0['keypoints'],
    'scores0': pred0['scores'],
    'descriptors0': pred0['descriptors'],
    'image1': image1_tensor,
    'keypoints1': pred1['keypoints'],
    'scores1': pred1['scores'],
    'descriptors1': pred1['descriptors'],
})

# 打印匹配结果的键，以便了解返回的数据结构
print("匹配结果包含的键:", matches.keys())

# 提取匹配点
keypoints0 = pred0['keypoints'][0].cpu().numpy()
keypoints1 = pred1['keypoints'][0].cpu().numpy()

# 获取匹配结果 - 根据实际返回的键进行处理
if 'matches0' in matches:
    # 如果返回的是matches0格式
    matches0 = matches['matches0'][0].cpu().numpy()  # 添加[0]索引
    valid = matches0 > -1
    mkpts0 = keypoints0[valid]
    mkpts1 = keypoints1[matches0[valid]]
elif 'keypoints0' in matches and 'keypoints1' in matches:
    # 如果返回的是直接的匹配点
    mkpts0 = matches['keypoints0'].cpu().numpy()
    mkpts1 = matches['keypoints1'].cpu().numpy()
else:
    # 如果是其他格式，尝试查找其他可能的键
    print("未找到标准匹配点格式，尝试其他可能的键...")
    if 'mkpts0' in matches and 'mkpts1' in matches:
        mkpts0 = matches['mkpts0'].cpu().numpy()
        mkpts1 = matches['mkpts1'].cpu().numpy()
    elif 'mkeypoints0' in matches and 'mkeypoints1' in matches:
        mkpts0 = matches['mkeypoints0'].cpu().numpy()
        mkpts1 = matches['mkeypoints1'].cpu().numpy()
    else:
        # 如果实在找不到匹配点，创建空数组
        print("无法找到匹配点，请检查匹配器返回的数据结构")
        mkpts0 = np.array([])
        mkpts1 = np.array([])

print(f"找到 {len(mkpts0)} 对匹配点")

# 计算单应性矩阵
inlier_mkpts0 = None
inlier_mkpts1 = None
if len(mkpts0) >= 4:
    # 使用RANSAC计算单应性矩阵
    H, mask = cv2.findHomography(mkpts0, mkpts1, cv2.RANSAC, 3.0)
    
    # 提取内点
    mask = mask.ravel().astype(bool)
    inlier_mkpts0 = mkpts0[mask]
    inlier_mkpts1 = mkpts1[mask]
    
    print("单应性矩阵 (Homography):")
    print(H)
    
    # 计算内点数量
    inliers = np.sum(mask)
    print(f"匹配点数量: {len(mkpts0)}, 内点数量: {inliers}")
else:
    print("匹配点不足，无法计算单应性矩阵")
    H = None

# 可视化匹配结果
def plot_matches(img1, img2, kpts1, kpts2, color=(0, 255, 0)):
    # 创建拼接图像
    h1, w1 = img1.shape[:2]
    h2, w2 = img2.shape[:2]
    height = max(h1, h2)
    width = w1 + w2
    vis = np.zeros((height, width, 3), np.uint8)
    vis[:h1, :w1] = img1
    vis[:h2, w1:w1+w2] = img2
    
    # 绘制匹配线
    for pt1, pt2 in zip(kpts1, kpts2):
        pt1 = tuple(map(int, pt1))
        pt2 = (int(pt2[0] + w1), int(pt2[1]))
        cv2.circle(vis, pt1, 3, color, -1)
        cv2.circle(vis, pt2, 3, color, -1)
        cv2.line(vis, pt1, pt2, color, 1)
    
    return vis

# 只有在有内点的情况下才绘制
if inlier_mkpts0 is not None and len(inlier_mkpts0) > 0:
    # 绘制内点匹配结果
    match_vis = plot_matches(image0, image1, inlier_mkpts0, inlier_mkpts1, color=(0, 255, 0))
    
    plt.figure(figsize=(20, 10))
    plt.imshow(match_vis)
    plt.title(f'SFD2+IMP result: {len(inlier_mkpts0)} inliers (total: {len(mkpts0)})')
    plt.axis('off')
    plt.savefig('匹配结果_内点.png', bbox_inches='tight')
    plt.show()

    # 如果需要，可以保存匹配结果
    np.savez('匹配结果_内点.npz', 
             keypoints0=inlier_mkpts0, 
             keypoints1=inlier_mkpts1, 
             homography=H)

    print("内点匹配结果已保存")
elif len(mkpts0) > 0:
    print("没有找到内点，显示所有匹配点")
    # 如果没有内点但有匹配点，显示所有匹配点
    match_vis = plot_matches(image0, image1, mkpts0, mkpts1, color=(255, 0, 0))
    
    plt.figure(figsize=(20, 10))
    plt.imshow(match_vis)
    plt.title(f'SFD2+IMP 匹配结果: {len(mkpts0)} 对匹配点 (无内点)')
    plt.axis('off')
    plt.savefig('匹配结果_所有点.png', bbox_inches='tight')
    plt.show()
else:
    print("没有找到匹配点，无法生成可视化结果")