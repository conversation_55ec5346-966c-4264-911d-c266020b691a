{"cells": [{"cell_type": "code", "execution_count": 1, "id": "5dc545c2-fe6a-4846-aaf7-8fb22bd4be9d", "metadata": {}, "outputs": [], "source": ["import cv2\n", "from imcui.ui.utils import DEVICE\n", "from imcui.api import ImageMatchingAPI\n", "from imcui.ui.viz import display_matches"]}, {"cell_type": "markdown", "id": "a2e18a24", "metadata": {}, "source": ["## Loading images"]}, {"cell_type": "code", "execution_count": 2, "id": "f7b83a1b-d961-4165-970c-da6254e19704", "metadata": {}, "outputs": [], "source": ["img_path1 = \"data/02928139_3448003521.jpg\"\n", "img_path2 = \"data/17295357_9106075285.jpg\"\n", "image0 = cv2.imread(str(img_path1))[:, :, ::-1]  # RGB\n", "image1 = cv2.imread(str(img_path2))[:, :, ::-1]  # RGB"]}, {"cell_type": "markdown", "id": "24d11817", "metadata": {}, "source": ["## Dense matching"]}, {"cell_type": "code", "execution_count": null, "id": "73ecb33a", "metadata": {}, "outputs": [], "source": ["conf = {\n", "    \"matcher\": {\n", "        \"output\": \"matches-loftr\",\n", "        \"model\": {\n", "            \"name\": \"loftr\",\n", "            \"weights\": \"outdoor\",\n", "            \"max_keypoints\": 2000,\n", "            \"match_threshold\": 0.2,\n", "        },\n", "        \"preprocessing\": {\n", "            \"grayscale\": True,\n", "            \"resize_max\": 1024,\n", "            \"dfactor\": 8,\n", "            \"width\": 640,\n", "            \"height\": 480,\n", "            \"force_resize\": True,\n", "        },\n", "        \"max_error\": 1,\n", "        \"cell_size\": 1,\n", "    },\n", "    \"dense\": True,\n", "}\n", "\n", "api = ImageMatchingAPI(conf=conf, device=DEVICE)\n", "pred = api(image0, image1)\n", "assert pred is not None\n", "titles = [\"Image 0 - RANSAC matched keypoints\", \"Image 1 - RANSAC matched keypoints\"]\n", "output_matches_ransac, _ = display_matches(pred, titles=titles, tag=\"KPTS_RANSAC\")"]}, {"cell_type": "markdown", "id": "8c7e0f67", "metadata": {}, "source": ["## Sparse matching"]}, {"cell_type": "code", "execution_count": null, "id": "223265ee", "metadata": {}, "outputs": [], "source": ["conf = {\n", "    \"feature\": {\n", "        \"output\": \"feats-superpoint-n4096-rmax1600\",\n", "        \"model\": {\n", "            \"name\": \"superpoint\",\n", "            \"nms_radius\": 3,\n", "            \"max_keypoints\": 4096,\n", "            \"keypoint_threshold\": 0.000,\n", "        },\n", "        \"preprocessing\": {\n", "            \"grayscale\": True,\n", "            \"force_resize\": True,\n", "            \"resize_max\": 1600,\n", "            \"width\": 640,\n", "            \"height\": 480,\n", "            \"dfactor\": 8,\n", "        },\n", "    },\n", "    \"matcher\": {\n", "        \"output\": \"matches-NN-mutual\",\n", "        \"model\": {\n", "            \"name\": \"nearest_neighbor\",\n", "            \"do_mutual_check\": True,\n", "            \"match_threshold\": 0.2,\n", "        },\n", "    },\n", "    \"dense\": <PERSON><PERSON><PERSON>,\n", "}\n", "\n", "api = ImageMatchingAPI(conf=conf, device=DEVICE)\n", "pred = api(image0, image1)\n", "assert pred is not None\n", "titles = [\"Image 0 - RANSAC matched keypoints\", \"Image 1 - RANSAC matched keypoints\"]\n", "output_matches_ransac, _ = display_matches(pred, titles=titles, tag=\"KPTS_RANSAC\")"]}], "metadata": {"kernelspec": {"display_name": "imw", "language": "python", "name": "imw"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 5}