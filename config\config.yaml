server:
  name: "0.0.0.0"
  port: 7860

defaults:
  setting_threshold: 0.1
  max_keypoints: 2000
  keypoint_threshold: 0.05
  enable_ransac: true
  ransac_method: CV2_USAC_MAGSAC
  ransac_reproj_threshold: 8
  ransac_confidence: 0.9999
  ransac_max_iter: 10000
  ransac_num_samples: 4
  match_threshold: 0.2
  setting_geometry: Homography

matcher_zoo:
  # example config
  Example:
    # show in `Matching Model` or not, default: true
    enable: false
    # matcher name
    matcher: example
    # skip ci or not, default: false
    skip_ci: true
    # dense matcher or not, default: true
    dense: true
    # info
    info:
      # dispaly name in `Matching Model`
      name: example(example)
      # conference/journal/workshop Year
      source: "CVPR XXXX"
      # github link
      github: https://github.com/example/example
      # paper link
      paper: https://arxiv.org/abs/xxxx.xxxx
      # project link
      project: https://example.com
      # show in `support algos` table
      display: false
      # low, medium, high
      efficiency: low

  dad(RoMa):
    matcher: dad_roma
    skip_ci: true
    dense: true
    info:
      name: Dad(<PERSON>o<PERSON><PERSON>) #dispaly name
      source: "ARXIV 2025"
      paper: https://arxiv.org/abs/2503.07347
      github: https://github.com/Parskatt/dad
      display: true
      efficiency: low  # low, medium, high
  minima(loftr):
    matcher: minima_loftr
    dense: true
    info:
      name: MINIMA(LoFTR) #dispaly name
      source: "ARXIV 2024"
      paper: https://arxiv.org/abs/2412.19412
      github: https://github.com/LSXI7/MINIMA
      display: true
  minima(RoMa):
    matcher: minima_roma
    skip_ci: true
    dense: true
    info:
      name: MINIMA(RoMa) #dispaly name
      source: "ARXIV 2024"
      paper: https://arxiv.org/abs/2412.19412
      github: https://github.com/LSXI7/MINIMA
      display: false
      efficiency: low  # low, medium, high
  omniglue:
    enable: true
    matcher: omniglue
    dense: true
    info:
      name: OmniGlue
      source: "CVPR 2024"
      github: https://github.com/Vincentqyw/omniglue-onnx
      paper: https://arxiv.org/abs/2405.12979
      project: https://hwjiang1510.github.io/OmniGlue
      display: true
  Mast3R:
    enable: false
    matcher: mast3r
    dense: true
    info:
      name: Mast3R #dispaly name
      source: "CVPR 2024"
      github: https://github.com/naver/mast3r
      paper: https://arxiv.org/abs/2406.09756
      project: https://dust3r.europe.naverlabs.com
      display: true
      efficiency: low  # low, medium, high
  DUSt3R:
    # TODO: duster is under development
    enable: true
    # skip_ci: true
    matcher: duster
    dense: true
    info:
      name: DUSt3R #dispaly name
      source: "CVPR 2024"
      github: https://github.com/naver/dust3r
      paper: https://arxiv.org/abs/2312.14132
      project: https://dust3r.europe.naverlabs.com
      display: true
  GIM(dkm):
    enable: true
    # skip_ci: true
    matcher: gim(dkm)
    dense: true
    info:
      name: GIM(DKM) #dispaly name
      source: "ICLR 2024"
      github: https://github.com/xuelunshen/gim
      paper: https://arxiv.org/abs/2402.11095
      project: https://xuelunshen.com/gim
      display: true
      efficiency: low  # low, medium, high
  RoMa:
    matcher: roma
    skip_ci: true
    dense: true
    info:
      name: RoMa #dispaly name
      source: "CVPR 2024"
      github: https://github.com/Parskatt/RoMa
      paper: https://arxiv.org/abs/2305.15404
      project: https://parskatt.github.io/RoMa
      display: true
      efficiency: low  # low, medium, high
  dkm:
    matcher: dkm
    skip_ci: true
    dense: true
    info:
      name: DKM #dispaly name
      source: "CVPR 2023"
      github: https://github.com/Parskatt/DKM
      paper: https://arxiv.org/abs/2202.00667
      project: https://parskatt.github.io/DKM
      display: true
      efficiency: low  # low, medium, high
  loftr:
    matcher: loftr
    dense: true
    info:
      name: LoFTR #dispaly name
      source: "CVPR 2021"
      github: https://github.com/zju3dv/LoFTR
      paper: https://arxiv.org/pdf/2104.00680
      project: https://zju3dv.github.io/loftr
      display: true
  eloftr:
    matcher: eloftr
    dense: true
    info:
      name: Efficient LoFTR #dispaly name
      source: "CVPR 2024"
      github: https://github.com/zju3dv/efficientloftr
      paper: https://zju3dv.github.io/efficientloftr/files/EfficientLoFTR.pdf
      project: https://zju3dv.github.io/efficientloftr
      display: true
  xoftr:
    matcher: xoftr
    dense: true
    info:
      name: XoFTR #dispaly name
      source: "CVPR 2024"
      github: https://github.com/OnderT/XoFTR
      paper: https://arxiv.org/pdf/2404.09692
      project: null
      display: true
  cotr:
    enable: false
    skip_ci: true
    matcher: cotr
    dense: true
    info:
      name: CoTR #dispaly name
      source: "ICCV 2021"
      github: https://github.com/ubc-vision/COTR
      paper: https://arxiv.org/abs/2103.14167
      project: null
      display: true
      efficiency: low  # low, medium, high
  topicfm:
    matcher: topicfm
    dense: true
    info:
      name: TopicFM #dispaly name
      source: "AAAI 2023"
      github: https://github.com/TruongKhang/TopicFM
      paper: https://arxiv.org/abs/2307.00485
      project: null
      display: true
  aspanformer:
    matcher: aspanformer
    dense: true
    info:
      name: ASpanformer #dispaly name
      source: "ECCV 2022"
      github: https://github.com/Vincentqyw/ml-aspanformer
      paper: https://arxiv.org/abs/2208.14201
      project: null
      display: true
  xfeat+lightglue:
    enable: true
    matcher: xfeat_lightglue
    dense: true
    info:
      name: xfeat+lightglue
      source: "CVPR 2024"
      github: https://github.com/Vincentqyw/omniglue-onnx
      paper: https://arxiv.org/abs/2405.12979
      project: https://hwjiang1510.github.io/OmniGlue
      display: true
  xfeat(sparse):
    matcher: NN-mutual
    feature: xfeat
    dense: false
    info:
      name: XFeat #dispaly name
      source: "CVPR 2024"
      github: https://github.com/verlab/accelerated_features
      paper: https://arxiv.org/abs/2404.19174
      project: null
      display: true
  xfeat(dense):
    matcher: xfeat_dense
    dense: true
    info:
      name: XFeat #dispaly name
      source: "CVPR 2024"
      github: https://github.com/verlab/accelerated_features
      paper: https://arxiv.org/abs/2404.19174
      project: null
      display: false
  dedode:
    matcher: Dual-Softmax
    feature: dedode
    dense: false
    info:
      name: DeDoDe #dispaly name
      source: "3DV 2024"
      github: https://github.com/Parskatt/DeDoDe
      paper: https://arxiv.org/abs/2308.08479
      project: null
      display: true
  superpoint+superglue:
    matcher: superglue
    feature: superpoint_max
    dense: false
    info:
      name: SuperGlue #dispaly name
      source: "CVPR 2020"
      github: https://github.com/magicleap/SuperGluePretrainedNetwork
      paper: https://arxiv.org/abs/1911.11763
      project: null
      display: true
  superpoint+lightglue:
    matcher: superpoint-lightglue
    feature: superpoint_max
    dense: false
    info:
      name: LightGlue #dispaly name
      source: "ICCV 2023"
      github: https://github.com/cvg/LightGlue
      paper: https://arxiv.org/pdf/2306.13643
      project: null
      display: true
  disk:
    matcher: NN-mutual
    feature: disk
    dense: false
    info:
      name: DISK
      source: "NeurIPS 2020"
      github: https://github.com/cvlab-epfl/disk
      paper: https://arxiv.org/abs/2006.13566
      project: null
      display: true
  disk+dualsoftmax:
    matcher: Dual-Softmax
    feature: disk
    dense: false
    info:
      name: DISK
      source: "NeurIPS 2020"
      github: https://github.com/cvlab-epfl/disk
      paper: https://arxiv.org/abs/2006.13566
      project: null
      display: false
  superpoint+dualsoftmax:
    matcher: Dual-Softmax
    feature: superpoint_max
    dense: false
    info:
      name: SuperPoint
      source: "CVPRW 2018"
      github: https://github.com/magicleap/SuperPointPretrainedNetwork
      paper: https://arxiv.org/abs/1712.07629
      project: null
      display: false
  sift+lightglue:
    matcher: sift-lightglue
    feature: sift
    dense: false
    info:
      name: LightGlue #dispaly name
      source: "ICCV 2023"
      github: https://github.com/cvg/LightGlue
      paper: https://arxiv.org/pdf/2306.13643
      project: null
      display: true
  disk+lightglue:
    matcher: disk-lightglue
    feature: disk
    dense: false
    info:
      name: LightGlue
      source: "ICCV 2023"
      github: https://github.com/cvg/LightGlue
      paper: https://arxiv.org/pdf/2306.13643
      project: null
      display: true
  aliked+lightglue:
    matcher: aliked-lightglue
    feature: aliked-n16
    dense: false
    info:
      name: ALIKED
      source: "ICCV 2023"
      github: https://github.com/Shiaoming/ALIKED
      paper: https://arxiv.org/pdf/2304.03608.pdf
      project: null
      display: true
  superpoint+mnn:
    matcher: NN-mutual
    feature: superpoint_max
    dense: false
    info:
      name: SuperPoint #dispaly name
      source: "CVPRW 2018"
      github: https://github.com/magicleap/SuperPointPretrainedNetwork
      paper: https://arxiv.org/abs/1712.07629
      project: null
      display: true
  sift+sgmnet:
    matcher: sgmnet
    feature: sift
    dense: false
    info:
      name: SGMNet #dispaly name
      source: "ICCV 2021"
      github: https://github.com/vdvchen/SGMNet
      paper: https://arxiv.org/abs/2108.08771
      project: null
      display: true
  sosnet:
    matcher: NN-mutual
    feature: sosnet
    dense: false
    info:
      name: SOSNet #dispaly name
      source: "CVPR 2019"
      github: https://github.com/scape-research/SOSNet
      paper: https://arxiv.org/abs/1904.05019
      project: https://research.scape.io/sosnet
      display: true
  hardnet:
    matcher: NN-mutual
    feature: hardnet
    dense: false
    info:
      name: HardNet #dispaly name
      source: "NeurIPS 2017"
      github: https://github.com/DagnyT/hardnet
      paper: https://arxiv.org/abs/1705.10872
      project: null
      display: true
  d2net:
    matcher: NN-mutual
    feature: d2net-ss
    dense: false
    info:
      name: D2Net #dispaly name
      source: "CVPR 2019"
      github: https://github.com/Vincentqyw/d2-net
      paper: https://arxiv.org/abs/1905.03561
      project: https://dusmanu.com/publications/d2-net.html
      display: true
  rord:
    matcher: NN-mutual
    feature: rord
    dense: false
    info:
      name: RoRD #dispaly name
      source: "IROS 2021"
      github: https://github.com/UditSinghParihar/RoRD
      paper: https://arxiv.org/abs/2103.08573
      project: https://uditsinghparihar.github.io/RoRD
      display: true
  alike:
    matcher: NN-mutual
    feature: alike
    dense: false
    info:
      name: ALIKE #dispaly name
      source: "TMM 2022"
      github: https://github.com/Shiaoming/ALIKE
      paper: https://arxiv.org/abs/2112.02906
      project: null
      display: true
  lanet:
    matcher: NN-mutual
    feature: lanet
    dense: false
    info:
      name: LANet #dispaly name
      source: "ACCV 2022"
      github: https://github.com/wangch-g/lanet
      paper: https://openaccess.thecvf.com/content/ACCV2022/papers/Wang_Rethinking_Low-level_Features_for_Interest_Point_Detection_and_Description_ACCV_2022_paper.pdf
      project: null
      display: true
  r2d2:
    matcher: NN-mutual
    feature: r2d2
    dense: false
    info:
      name: R2D2 #dispaly name
      source: "NeurIPS 2019"
      github: https://github.com/naver/r2d2
      paper: https://arxiv.org/abs/1906.06195
      project: null
      display: true
  darkfeat:
    matcher: NN-mutual
    feature: darkfeat
    dense: false
    info:
      name: DarkFeat #dispaly name
      source: "AAAI 2023"
      github: https://github.com/THU-LYJ-Lab/DarkFeat
      paper: null
      project: null
      display: true
  sift:
    matcher: NN-mutual
    feature: sift
    dense: false
    info:
      name: SIFT #dispaly name
      source: "IJCV 2004"
      github: null
      paper: https://www.cs.ubc.ca/~lowe/papers/ijcv04.pdf
      project: null
      display: true
  gluestick:
    enable: true
    matcher: gluestick
    dense: true
    info:
      name: GlueStick #dispaly name
      source: "ICCV 2023"
      github: https://github.com/cvg/GlueStick
      paper: https://arxiv.org/abs/2304.02008
      project: https://iago-suarez.com/gluestick
      display: true
  sold2:
    enable: false
    matcher: sold2
    dense: true
    info:
      name: SOLD2 #dispaly name
      source: "CVPR 2021"
      github: https://github.com/cvg/SOLD2
      paper: https://arxiv.org/abs/2104.03362
      project: null
      display: true

  sfd2+imp:
    enable: true
    matcher: imp
    feature: sfd2
    dense: false
    info:
      name: SFD2+IMP #dispaly name
      source: "CVPR 2023"
      github: https://github.com/feixue94/imp-release
      paper: https://arxiv.org/pdf/2304.14837
      project: https://feixue94.github.io/
      display: true

  sfd2+mnn:
    enable: true
    matcher: NN-mutual
    feature: sfd2
    dense: false
    info:
      name: SFD2+MNN #dispaly name
      source: "CVPR 2023"
      github: https://github.com/feixue94/sfd2
      paper: https://arxiv.org/abs/2304.14845
      project: https://feixue94.github.io/
      display: true

retrieval_zoo:
  netvlad:
    enable: true
  openibl:
    enable: true
  cosplace:
    enable: true
