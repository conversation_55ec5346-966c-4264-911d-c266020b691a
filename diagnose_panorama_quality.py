#!/usr/bin/env python3
"""
诊断全景图质量问题的工具
分析累积误差和全局一致性问题
"""

import cv2
import numpy as np
import json
from pathlib import Path
import argparse

from src.images import Image
from src.matching.imp_matcher import IMPMatcher
from src.matching.pair_match import PairMatch
from src.config import get_config
from src.matching.multi_images_matches import natural_sort_key

def analyze_homography_chain(images, config_mode="default"):
    """分析单应性矩阵链的累积误差"""
    
    print("🔍 分析单应性矩阵链...")
    
    # 获取配置
    config = get_config(config_mode)
    Image.sfd2_extractor = Image.sfd2_extractor.__class__(config['sfd2'])
    
    # 创建匹配器
    matcher = IMPMatcher(
        match_threshold=config['imp']['match_threshold'],
        max_keypoints=config['imp']['max_keypoints']
    )
    
    homographies = []
    cumulative_H = np.eye(3)  # 累积单应性矩阵
    
    print(f"{'步骤':<6} {'图像对':<15} {'内点数':<8} {'内点率':<10} {'累积条件数':<12}")
    print("-" * 60)
    
    for i in range(len(images) - 1):
        image_a = images[i]
        image_b = images[i + 1]
        
        # 提取特征
        image_a.compute_features()
        image_b.compute_features()
        
        # 匹配
        mkpts0, mkpts1 = matcher.match(image_a, image_b)
        
        if len(mkpts0) < 4:
            print(f"{i+1:<6} {Path(image_a.path).stem}→{Path(image_b.path).stem:<15} {'失败':<8} {'N/A':<10} {'N/A':<12}")
            continue
        
        # 计算单应性矩阵
        H, status = cv2.findHomography(mkpts1, mkpts0, cv2.RANSAC, 5.0, maxIters=500)
        
        if H is not None:
            inliers = np.sum(status)
            inlier_ratio = inliers / len(mkpts0)
            
            # 累积单应性矩阵
            cumulative_H = cumulative_H @ H
            
            # 计算条件数（衡量矩阵的数值稳定性）
            cond_num = np.linalg.cond(cumulative_H)
            
            homographies.append({
                'step': i + 1,
                'pair': f"{Path(image_a.path).stem}→{Path(image_b.path).stem}",
                'H': H,
                'cumulative_H': cumulative_H.copy(),
                'inliers': int(inliers),
                'total_matches': len(mkpts0),
                'inlier_ratio': float(inlier_ratio),
                'condition_number': float(cond_num)
            })
            
            print(f"{i+1:<6} {Path(image_a.path).stem}→{Path(image_b.path).stem:<15} "
                  f"{inliers:<8} {inlier_ratio:<10.1%} {cond_num:<12.2e}")
        else:
            print(f"{i+1:<6} {Path(image_a.path).stem}→{Path(image_b.path).stem:<15} {'失败':<8} {'N/A':<10} {'N/A':<12}")
    
    return homographies

def analyze_drift_error(homographies):
    """分析漂移误差"""
    
    print(f"\n📊 漂移误差分析:")
    print("-" * 40)
    
    if not homographies:
        print("❌ 没有有效的单应性矩阵")
        return
    
    # 分析平移漂移
    translations = []
    rotations = []
    scales = []
    
    for h in homographies:
        H = h['cumulative_H']
        
        # 提取平移
        tx, ty = H[0, 2], H[1, 2]
        translations.append((tx, ty))
        
        # 提取旋转和缩放
        a, b = H[0, 0], H[0, 1]
        scale = np.sqrt(a*a + b*b)
        rotation = np.arctan2(b, a) * 180 / np.pi
        
        scales.append(scale)
        rotations.append(rotation)
    
    # 计算漂移
    total_translation = np.sqrt(translations[-1][0]**2 + translations[-1][1]**2)
    total_rotation = abs(rotations[-1])
    total_scale_change = abs(scales[-1] - 1.0)
    
    print(f"总平移漂移: {total_translation:.1f} 像素")
    print(f"总旋转漂移: {total_rotation:.2f} 度")
    print(f"总缩放变化: {total_scale_change:.3f}")
    
    # 条件数分析
    condition_numbers = [h['condition_number'] for h in homographies]
    max_cond = max(condition_numbers)
    
    print(f"最大条件数: {max_cond:.2e}")
    
    if max_cond > 1e12:
        print("⚠️  条件数过大，矩阵接近奇异，数值不稳定")
    elif max_cond > 1e8:
        print("⚠️  条件数较大，可能存在数值问题")
    else:
        print("✅ 条件数正常")
    
    return {
        'total_translation': total_translation,
        'total_rotation': total_rotation,
        'total_scale_change': total_scale_change,
        'max_condition_number': max_cond
    }

def create_step_by_step_visualization(images, homographies, output_dir):
    """创建逐步拼接的可视化"""
    
    print(f"\n🎨 创建逐步拼接可视化...")
    
    output_dir = Path(output_dir)
    step_dir = output_dir / "step_by_step"
    step_dir.mkdir(exist_ok=True)
    
    # 从第一张图像开始
    panorama = images[0].image.copy()
    cumulative_H = np.eye(3)
    
    # 保存第一张图像
    cv2.imwrite(str(step_dir / "step_00_base.jpg"), panorama)
    
    for i, h in enumerate(homographies):
        if 'H' not in h:
            continue
            
        # 获取下一张图像
        next_image = images[i + 1].image
        
        # 累积变换
        cumulative_H = cumulative_H @ h['H']
        
        # 计算新的画布尺寸
        h_pano, w_pano = panorama.shape[:2]
        h_next, w_next = next_image.shape[:2]
        
        # 变换下一张图像的四个角点
        corners_next = np.float32([[0, 0], [w_next, 0], [w_next, h_next], [0, h_next]]).reshape(-1, 1, 2)
        transformed_corners = cv2.perspectiveTransform(corners_next, cumulative_H)
        
        # 计算新画布的边界
        all_corners = np.vstack([
            [[0, 0], [w_pano, 0], [w_pano, h_pano], [0, h_pano]],
            transformed_corners.reshape(-1, 2)
        ])
        
        x_min, y_min = np.min(all_corners, axis=0)
        x_max, y_max = np.max(all_corners, axis=0)
        
        # 创建平移矩阵
        translation = np.array([[1, 0, -x_min], [0, 1, -y_min], [0, 0, 1]])
        
        # 新画布尺寸
        new_width = int(x_max - x_min)
        new_height = int(y_max - y_min)
        
        # 变换全景图
        new_panorama = cv2.warpPerspective(panorama, translation, (new_width, new_height))
        
        # 变换并添加新图像
        final_H = translation @ cumulative_H
        warped_next = cv2.warpPerspective(next_image, final_H, (new_width, new_height))
        
        # 简单叠加（可以改进为更好的融合）
        mask = (warped_next.sum(axis=2) > 0)
        new_panorama[mask] = warped_next[mask]
        
        panorama = new_panorama
        
        # 保存中间结果
        step_filename = f"step_{i+1:02d}_{Path(images[i].path).stem}_to_{Path(images[i+1].path).stem}.jpg"
        cv2.imwrite(str(step_dir / step_filename), panorama)
        
        print(f"  步骤 {i+1}: 添加 {Path(images[i+1].path).stem}, 画布尺寸: {new_width}x{new_height}")
    
    # 保存最终结果
    cv2.imwrite(str(step_dir / "final_panorama.jpg"), panorama)
    
    print(f"✅ 逐步可视化保存在: {step_dir}")

def main():
    parser = argparse.ArgumentParser(description="诊断全景图质量问题")
    parser.add_argument("data_dir", type=Path, help="图像目录")
    parser.add_argument("--output", type=str, default="panorama_diagnosis", help="输出目录")
    parser.add_argument("--config", type=str, default="default", help="配置模式")
    parser.add_argument("--size", type=int, default=1000, help="图像缩放尺寸")
    
    args = parser.parse_args()
    
    # 获取图像列表
    valid_exts = {".jpg", ".png", ".bmp", ".jpeg"}
    image_paths = sorted([
        p for p in args.data_dir.iterdir()
        if p.suffix.lower() in valid_exts
    ], key=lambda p: natural_sort_key(str(p)))
    
    print(f"📸 找到 {len(image_paths)} 张图像")
    
    # 加载图像
    images = [Image(str(path), args.size) for path in image_paths]
    
    # 创建输出目录
    output_dir = Path(args.output)
    output_dir.mkdir(exist_ok=True)
    
    # 分析单应性矩阵链
    homographies = analyze_homography_chain(images, args.config)
    
    # 分析漂移误差
    drift_analysis = analyze_drift_error(homographies)
    
    # 创建逐步可视化
    create_step_by_step_visualization(images, homographies, output_dir)
    
    # 保存分析结果
    analysis_result = {
        'total_images': len(images),
        'successful_matches': len(homographies),
        'drift_analysis': drift_analysis,
        'homographies': [
            {k: v for k, v in h.items() if k != 'H' and k != 'cumulative_H'}
            for h in homographies
        ]
    }
    
    with open(output_dir / "analysis_report.json", 'w', encoding='utf-8') as f:
        json.dump(analysis_result, f, indent=2, ensure_ascii=False)
    
    print(f"\n📁 完整分析结果保存在: {output_dir.absolute()}")

if __name__ == "__main__":
    main()
