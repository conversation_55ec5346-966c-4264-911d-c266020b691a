# 图像拼接系统 - CPU版本

## 版本信息
- **版本**: v1.0-CPU
- **日期**: 2025-07-10
- **类型**: CPU优化版本

## 主要特性

### ✅ 已修复的问题
1. **OpenCV 特征匹配兼容性问题**
   - 修复了 SFD2 特征与 OpenCV BruteForce 匹配器的数据类型不兼容问题
   - 实现了 IMPMatcher 与传统 OpenCV 匹配流程的桥接

2. **Keypoint 格式兼容性问题**
   - 支持 SFD2 numpy 数组格式的 keypoints
   - 兼容传统 OpenCV KeyPoint 对象格式

3. **匹配点数量不足问题**
   - 添加了匹配点数量检查和错误处理
   - 降低了 IMP 匹配阈值以获得更多匹配点

4. **文件名排序问题**
   - 实现了自然排序，正确处理数字文件名 (0, 1, 2, 10, 100...)
   - 避免了字符串排序导致的错误顺序 (1, 10, 100, 2, 3...)

### 🚀 性能优化特性

#### 1. 顺序匹配算法
- **复杂度优化**: O(n²) → O(n)
- **速度提升**: 比全匹配快 5.9倍
- **适用场景**: 按顺序拍摄的图像序列

#### 2. 可配置匹配范围
- `--seq-range 1`: 仅匹配相邻图像 (最快)
- `--seq-range 2`: 匹配相邻+隔一张图像 (更稳定)

#### 3. 自然文件名排序
- 支持纯数字文件名: `0.bmp, 1.bmp, 10.bmp, 100.bmp`
- 支持带前缀文件名: `img_1.jpg, img_2.jpg, img_10.jpg`
- 支持复杂文件名: `frame_001_part_1.png`

## 使用方法

### 基本命令
```bash
# 标准拼接 (全匹配)
python nnewmain.py ../testdata --size 1000 -v

# 顺序匹配 (推荐，快5.9倍)
python nnewmain.py ../testdata --sequential --size 1000 -v

# 多频带融合
python nnewmain.py ../testdata --sequential --multi-band --size 1000 -v

# 扩大匹配范围
python nnewmain.py ../testdata --sequential --seq-range 2 --size 1000 -v
```

### 性能测试
```bash
# 性能对比测试
python benchmark_matching.py ../testdata --size 1000

# 详细性能分析
python performance_test.py ../testdata --count 6
```

## 性能表现

### 测试环境
- 图像数量: 6张 (1000px)
- CPU: 标准桌面处理器
- 内存: 充足

### 性能对比
| 匹配策略 | 耗时 | 速度提升 | 匹配对数 |
|---------|------|----------|----------|
| 全匹配 | 141.37s | 1.0x | 9对 |
| 顺序匹配(range=1) | 23.84s | 5.9x | 5对 |
| 顺序匹配(range=2) | 41.30s | 3.4x | 9对 |

### 时间分布
- 特征提取: 19.5%
- 特征匹配: 63.8%
- 模块导入: 14.5%
- 图像I/O: 0.2%

## 技术架构

### 核心组件
1. **SFD2 特征提取器**: 深度学习特征提取
2. **IMP 特征匹配器**: 神经网络特征匹配
3. **MultiImageMatches**: 图像匹配管理
4. **PairMatch**: 图像对匹配处理
5. **自然排序**: 智能文件名排序

### 关键文件
- `nnewmain.py`: 主程序入口
- `src/matching/multi_images_matches.py`: 匹配逻辑
- `src/matching/imp_matcher.py`: IMP匹配器封装
- `src/matching/pair_match.py`: 图像对处理
- `src/images/image.py`: 图像类定义

## 依赖要求
```
torch>=1.9.0
torchvision>=0.10.0
opencv-python>=4.5.0
numpy>=1.21.0
pathlib
```

## 已知限制
1. **仅CPU计算**: 未使用GPU加速
2. **内存占用**: 大量图像时内存需求较高
3. **模型加载**: 每次启动都需要加载深度学习模型

## 下一步计划
1. **GPU加速版本**: 利用CUDA加速深度学习计算
2. **内存优化**: 实现流式处理减少内存占用
3. **模型缓存**: 避免重复加载模型
4. **并行处理**: 多线程/多进程优化

## 使用建议
- ✅ **推荐**: 使用顺序匹配处理按序拍摄的图像
- ✅ **推荐**: 根据硬件能力调整 `--size` 参数
- ✅ **推荐**: 使用多频带融合获得更好的拼接质量
- ⚠️ **注意**: 大量图像时考虑分批处理
- ⚠️ **注意**: 确保图像文件名反映拍摄顺序
