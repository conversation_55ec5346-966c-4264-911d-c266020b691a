#!/usr/bin/env python3
"""
拼接两张指定图像，保留原图像像素，不进行压缩
"""

import cv2
import numpy as np
import argparse
from pathlib import Path

from src.images import Image
from src.matching.imp_matcher import IMPMatcher
from src.matching.pair_match import PairMatch
from src.config import get_config

def stitch_two_images_no_resize(image_path_a, image_path_b, output_path="two_image_stitch.jpg", config_mode="default"):
    """
    拼接两张图像，保留原始像素，不进行压缩
    
    Args:
        image_path_a: 第一张图像路径
        image_path_b: 第二张图像路径
        output_path: 输出路径
        config_mode: 配置模式
    """
    
    print(f"🔍 拼接两张图像:")
    print(f"  图像A: {Path(image_path_a).name}")
    print(f"  图像B: {Path(image_path_b).name}")
    print(f"  配置: {config_mode}")
    print(f"  输出: {output_path}")
    print("-" * 50)
    
    # 获取配置
    config = get_config(config_mode)
    print(f"SFD2参数: max_keypoints={config['sfd2']['max_keypoints']}, threshold={config['sfd2']['keypoint_threshold']}")
    print(f"IMP参数: match_threshold={config['imp']['match_threshold']}")
    
    # 加载图像 - 不进行缩放，保持原始尺寸
    print(f"\n📸 加载原始图像...")
    image_a = Image(image_path_a, size=None)  # size=None 表示不缩放
    image_b = Image(image_path_b, size=None)
    
    print(f"  图像A尺寸: {image_a.image.shape}")
    print(f"  图像B尺寸: {image_b.image.shape}")
    
    # 应用配置
    Image.sfd2_extractor = Image.sfd2_extractor.__class__(config['sfd2'])
    
    # 提取特征
    print(f"\n🔧 提取特征...")
    image_a.compute_features()
    image_b.compute_features()
    
    print(f"  图像A特征点数: {len(image_a.keypoints)}")
    print(f"  图像B特征点数: {len(image_b.keypoints)}")
    
    # 创建匹配器
    matcher = IMPMatcher(
        match_threshold=config['imp']['match_threshold'],
        max_keypoints=config['imp']['max_keypoints']
    )
    
    # 进行匹配
    print(f"\n🎯 进行特征匹配...")
    mkpts0, mkpts1 = matcher.match(image_a, image_b)
    print(f"  原始匹配点数: {len(mkpts0)}")
    
    if len(mkpts0) < 4:
        print("❌ 匹配点不足，无法进行拼接")
        return False
    
    # 转换为DMatch格式
    matches = []
    for i in range(len(mkpts0)):
        distances_a = np.linalg.norm(image_a.keypoints - mkpts0[i], axis=1)
        distances_b = np.linalg.norm(image_b.keypoints - mkpts1[i], axis=1)
        
        idx_a = np.argmin(distances_a)
        idx_b = np.argmin(distances_b)
        
        if distances_a[idx_a] < 1e-6 and distances_b[idx_b] < 1e-6:
            match = cv2.DMatch(idx_a, idx_b, 0.0)
            matches.append(match)
    
    print(f"  有效匹配数: {len(matches)}")
    
    # 创建PairMatch并计算单应性矩阵
    print(f"\n🎲 计算单应性矩阵...")
    pair_match = PairMatch(image_a, image_b, matches)
    pair_match.compute_homography()
    
    if pair_match.H is None:
        print("❌ 单应性矩阵计算失败")
        return False
    
    # 计算内点信息
    inliers = np.sum(pair_match.status) if pair_match.status is not None else 0
    inlier_ratio = inliers / len(matches) if matches else 0
    print(f"  内点数: {inliers}/{len(matches)} ({inlier_ratio:.1%})")
    
    # 验证匹配质量
    is_valid = pair_match.is_valid()
    print(f"  匹配验证: {'✅ 通过' if is_valid else '❌ 失败'}")
    
    if not is_valid:
        # 尝试宽松验证
        is_valid_loose = pair_match.is_valid(alpha=4, beta=0.1)
        print(f"  宽松验证: {'✅ 通过' if is_valid_loose else '❌ 失败'}")
        
        if not is_valid_loose:
            print("⚠️  匹配质量较低，但继续拼接...")
    
    # 开始拼接
    print(f"\n🎨 开始拼接...")
    
    H = pair_match.H
    img_a = image_a.image
    img_b = image_b.image
    
    h1, w1 = img_a.shape[:2]
    h2, w2 = img_b.shape[:2]
    
    print(f"  原始图像A: {w1}x{h1}")
    print(f"  原始图像B: {w2}x{h2}")
    
    # 计算变换后的边界
    corners_b = np.float32([[0, 0], [w2, 0], [w2, h2], [0, h2]]).reshape(-1, 1, 2)
    transformed_corners = cv2.perspectiveTransform(corners_b, H)
    
    # 计算新画布的边界
    all_corners = np.vstack([
        [[0, 0], [w1, 0], [w1, h1], [0, h1]],
        transformed_corners.reshape(-1, 2)
    ])
    
    x_min, y_min = np.min(all_corners, axis=0)
    x_max, y_max = np.max(all_corners, axis=0)
    
    # 新画布尺寸
    new_width = int(x_max - x_min)
    new_height = int(y_max - y_min)
    
    print(f"  全景图尺寸: {new_width}x{new_height}")
    
    # 检查尺寸是否合理
    if new_width > 50000 or new_height > 50000:
        print(f"⚠️  全景图尺寸过大: {new_width}x{new_height}")
        print("   这可能表示匹配质量有问题")
        
        # 询问是否继续
        response = input("   是否继续拼接? (y/n): ")
        if response.lower() != 'y':
            return False
    
    # 创建平移矩阵
    translation = np.array([[1, 0, -x_min], [0, 1, -y_min], [0, 0, 1]])
    
    # 创建全景图画布
    panorama = np.zeros((new_height, new_width, 3), dtype=np.uint8)
    
    # 变换并放置图像A
    img_a_transformed = cv2.warpPerspective(img_a, translation, (new_width, new_height))
    
    # 变换并放置图像B
    final_H = translation @ H
    img_b_transformed = cv2.warpPerspective(img_b, final_H, (new_width, new_height))
    
    # 融合图像
    print(f"  融合图像...")
    
    # 创建掩码
    mask_a = (img_a_transformed.sum(axis=2) > 0)
    mask_b = (img_b_transformed.sum(axis=2) > 0)
    overlap = mask_a & mask_b
    
    # 非重叠区域直接复制
    panorama[mask_a & ~overlap] = img_a_transformed[mask_a & ~overlap]
    panorama[mask_b & ~overlap] = img_b_transformed[mask_b & ~overlap]
    
    # 重叠区域使用简单平均
    if np.any(overlap):
        panorama[overlap] = (img_a_transformed[overlap].astype(np.float32) + 
                           img_b_transformed[overlap].astype(np.float32)) / 2
        panorama[overlap] = panorama[overlap].astype(np.uint8)
        print(f"  重叠区域像素数: {np.sum(overlap)}")
    
    # 保存结果
    print(f"\n💾 保存结果...")
    success = cv2.imwrite(output_path, panorama)
    
    if success:
        print(f"✅ 拼接成功!")
        print(f"📁 保存到: {Path(output_path).absolute()}")
        print(f"📊 最终尺寸: {new_width}x{new_height}")
        
        # 计算文件大小
        file_size = Path(output_path).stat().st_size / (1024 * 1024)  # MB
        print(f"📦 文件大小: {file_size:.1f} MB")
        
        return True
    else:
        print(f"❌ 保存失败")
        return False

def main():
    parser = argparse.ArgumentParser(description="拼接两张指定图像，保留原始像素")
    parser.add_argument("image_a", type=str, help="第一张图像路径")
    parser.add_argument("image_b", type=str, help="第二张图像路径")
    parser.add_argument("--output", type=str, default="two_image_stitch.jpg", help="输出文件路径")
    parser.add_argument("--config", type=str, default="default", 
                       choices=["default", "fast", "loose", "ultra_loose", "high_quality", "super_high_quality"],
                       help="配置模式")
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not Path(args.image_a).exists():
        print(f"❌ 图像A不存在: {args.image_a}")
        return
    
    if not Path(args.image_b).exists():
        print(f"❌ 图像B不存在: {args.image_b}")
        return
    
    # 执行拼接
    success = stitch_two_images_no_resize(args.image_a, args.image_b, args.output, args.config)
    
    if success:
        print(f"\n🎉 拼接完成!")
    else:
        print(f"\n💥 拼接失败!")

if __name__ == "__main__":
    main()
